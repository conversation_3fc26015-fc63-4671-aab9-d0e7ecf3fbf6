@font-face {
    font-family: 'iconfont';  /* project id 1867770 */
    src: url('https://at.alicdn.com/t/font_1867770_aheoboelyyp.eot');
    src: url('https://at.alicdn.com/t/font_1867770_aheoboelyyp.eot?#iefix') format('embedded-opentype'),
    url('https://at.alicdn.com/t/font_1867770_aheoboelyyp.woff2') format('woff2'),
    url('https://at.alicdn.com/t/font_1867770_aheoboelyyp.woff') format('woff'),
    url('https://at.alicdn.com/t/font_1867770_aheoboelyyp.ttf') format('truetype'),
    url('https://at.alicdn.com/t/font_1867770_aheoboelyyp.svg#iconfont') format('svg');
}

.sv-target video {
    background-color: #000000;
}

.sv-font {
    font-family: 'iconfont';
}

.sv-play {
    color: #ffffff;
    font-size: 20px;
}

.sv-next {
    color: #ffffff;
    font-size: 20px;
}

.sv-fullScreen {
    color: #ffffff;
    font-size: 20px;
}

.sv-cancelFull {
    color: #ffffff;
    font-size: 20px;
}

.sv-target {
    position: relative;
}
.sv-control {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 44px;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.sv-play-container {
    height: 100%;
    /* background-color: royalblue; */
    display: flex;
    flex-direction: row;
    padding-right: 10px;
}
.sv-control-r {
    height: 100%;
    /* background-color: royalblue; */
    display: flex;
    flex-direction: row;
    padding-left: 10px;
    padding-right: 10px;
}
.sv-play-container button.sv-playBtn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 10px;
    outline: none;
    color: inherit;
    text-align: inherit;
    font: inherit;
    line-height: inherit;
    margin-left: 10px;
}

.sv-control-r button.showMute {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 10px;
    outline: none;
    color: inherit;
    text-align: inherit;
    font: inherit;
    line-height: inherit;
    position: relative;
}

.sv-time {
    height: 100%;
    color: #ffffff;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
}

.sv-time-split {
    padding: 0 4px;
}

.sv-mutePanel {
    position: absolute;
    top: -120px;
    left: 0;
    width: 20px;
    height: 80px;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    padding: 16px 6px;
    border-radius: 4px;
}

.sv-mute-num {
    width: 100%;
    height: 20px;
    background-color: transparent;
    color: #ffffff;
    font-size: 12px;
    text-align: center;
    margin-bottom: 4px;
}

.sv-mute-slider {
    flex: 1;
    width: 3px;
    background-color: #ffffff;
    margin: 0 auto;
    position: relative;
    display: flex;
    flex-direction: column-reverse;
}

.sv-mute-sliderRange {
    width: 100%;
    background-color: #00a1d6;
}

.sv-control-r button.sv-mute-button {
    position: absolute;
    top: 0;
    left: -4.5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    z-index: 10;
    background-color: #00a1d6;
    border: 0;
    cursor: pointer;
    outline: none;
}

.sv-progressBar {
    position: absolute;
    top: 0;
    left: 2%;
    width: 96%;
    height: 2px;
    background-color: hsla(0,0%,100%,.2);
    border-radius: 4px;
    cursor: pointer;
}

.sv-cacheProgress {
    width: 0%;
    height: 100%;
    background-color: #7a7878;
    border-radius: 4px;
}

.sv-progressNum {
    width: 0%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 4px;
    background-color: #00a1d6;
}

.sv-progressBtn {
    position: absolute;
    left: 0%;
    top: -7px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: rgba(0, 161, 214, 0.5);
    cursor: pointer;
}
.sv-progressBtn>div {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #00a1d6;
    margin-top: 2.6px;
    margin-left: 2.8px;
}

.hide {
    display: none;
}

.sv-full-screen {
    position: fixed!important;
    width: 100%!important;
    height: 100%!important;
    left: 0!important;
    top: 0!important;
}.sv-el-control-style {
    padding-right: 10px;
    padding-left: 10px;
    height: 100%;
    display: flex;
    flex-direction: row;
}

.sv-nextBtn {
    background: none;
    border: none;
    cursor: pointer;
    outline: none;
    text-align: inherit;
}

.sv-speedBtn {
    background: none;
    border: none;
    cursor: pointer;
    outline: none;
    text-align: inherit;
    position: relative;
}
.sv-speedBtn .sv-speed-btn {
    position: absolute;
    bottom: 54px;
    left: -10px;
    padding: 10px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.8);
    /* box-sizing: border-box;
    border: 1px solid #ffffff; */
}
.sv-speedBtn .sv-speed-btn ul {
    padding: 0;
    margin: 0;
}
.sv-speedBtn .sv-speed-btn ul li {
    list-style: none;
    font-size: 12px;
    line-height: 20px;
    cursor: pointer;
}
.sv-speedBtn .sv-speed-btn ul li:hover{
    color: #00a1d6;
}
.sv-active{
    color: #00a1d6!important;
}
