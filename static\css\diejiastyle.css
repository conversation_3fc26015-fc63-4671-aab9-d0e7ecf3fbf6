@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

.inner_container {
  position: fixed;
  max-width: 430px;
  width: 100%;
  padding: 15px;
  border-radius: 25px;
  box-sizing: border-box;
  bottom: 50px;
  left: 0px;
  z-index: 9999;
}
.inner_box {
  max-height: 70vh;
  overflow-y: auto;
}
.park_sec {
  position: relative;
  max-width: 100%;
  width: 100%;
  /* display: flex; */
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 10px;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  box-sizing: border-box;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: 'tnum';
  position: relative;
  padding: 8px 15px 8px 37px;
  word-wrap: break-word;
  border-radius: 4px;
}
.park_sec-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}
.park_sec-error {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
}

.alert-description {
  position: relative;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.5;
  border-radius: 4px;
}
.alert-message {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.park_sec.active {
  transform: unset;
  display: block !important;
}
.park_sec1 {
  z-index: 2;
  transform: translateY(200%) scale(0.95);
}
.park_sec2 {
  z-index: 1;
  transform: translateY(100%) scale(0.9);
}
.park_sec3 {
  z-index: 0;
  transform: translateY(0px) scale(0.85);
}
.park_sec.max {
  display: none;
}
.park_inside {
  position: relative;
  display: flex;
}
.content_sec {
  position: relative;
  margin-left: 10px;
}
.img {
  position: relative;
  width: 50px;
  height: 50px;
  background: #000;
  border-radius: 50%;
}
.content_sec h2 {
  position: relative;
  margin: 0px;
  font-size: 16px;
  font-weight: 500;
}
.content_sec span,
.park_sec span {
  position: relative;
  color: #000000;
  font-size: 15px;
}
.btn_grp {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn {
  position: relative;
  padding: 10px 40px 10px 30px;
  background: #fff;
  border-radius: 20px;
  border: 0px;
  box-sizing: border-box;
  box-shadow: 0px 3px 3.5px #777171;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
}
.btn::after {
  position: absolute;
  content: '';
  border-top: 2px solid #000;
  border-left: 2px solid #000;
  width: 7px;
  height: 7px;
  right: 23px;
  top: 12px;
  transform: rotate(225deg);
  transition: all 0.3s linear;
}
.btn.active::after {
  transform: rotate(45deg);
  top: 17px;
}
