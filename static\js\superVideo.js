!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("Super",[],t):"object"==typeof exports?exports.Super=t():e.Super=t()}(window,(function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=4)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();n(22);var r,i=n(1);var u=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.option=Object.assign({},{className:"sv-el-control"},e),n.element_=null,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"init_",value:function(e){return this.element_=document.createElement("div"),this.element_.className=this.option.className+" sv-el-control-style",this.video_=e,this.create_(),this.element_}},{key:"create_",value:function(){}}]),t}(((r=i)&&r.__esModule?r:{default:r}).default);t.default=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=s(n(6)),i=s(n(7)),u=n(8),a=n(10);function s(e){return e&&e.__esModule?e:{default:e}}var l=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.eventTarget_=e,n.pendingRemovals_={},n.dispatching_={},n.listeners_={},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"addEventListener",value:function(e,t){if(e&&t){var n=this.listeners_[e];n||(n=[],this.listeners_[e]=n),-1===n.indexOf(t)&&n.push(t)}}},{key:"dispatchEvent",value:function(e){var t="string"==typeof e?new i.default(e):e,n=t.type;t.target||(t.target=this.eventTarget_||this);var o=this.listeners_[n],r=void 0;if(o){n in this.dispatching_||(this.dispatching_[n]=0,this.pendingRemovals_[n]=0),++this.dispatching_[n];for(var a=0,s=o.length;a<s;++a)if(!1===(r="handleEvent"in o[a]?o[a].handleEvent(t):o[a].call(this,t))||t.propagationStopped){r=!1;break}if(--this.dispatching_[n],0===this.dispatching_[n]){var l=this.pendingRemovals_[n];for(delete this.pendingRemovals_[n];l--;)this.removeEventListener(n,u.VOID);delete this.dispatching_[n]}return r}}},{key:"disposeInternal",value:function(){(0,a.clear)(this.listeners_)}},{key:"getListeners",value:function(e){return this.listeners_[e]}},{key:"hasListener",value:function(e){return e?e in this.listeners_:Object.keys(this.listeners_).length>0}},{key:"removeEventListener",value:function(e,t){var n=this.listeners_[e];if(n){var o=n.indexOf(t);-1!==o&&(e in this.pendingRemovals_?(n[o]=u.VOID,++this.pendingRemovals_[e]):(n.splice(o,1),0===n.length&&delete this.listeners_[e]))}}}]),t}(r.default);t.default=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={CHANGE:"change",ERROR:"error",BLUR:"blur",CLEAR:"clear",CONTEXTMENU:"contextmenu",CLICK:"click",DBLCLICK:"dblclick",DRAGENTER:"dragenter",DRAGOVER:"dragover",DROP:"drop",FOCUS:"focus",KEYDOWN:"keydown",KEYPRESS:"keypress",LOAD:"load",RESIZE:"resize",TOUCHMOVE:"touchmove",WHEEL:"wheel",READY:"ready",FULLSCREEN:"fullscreen",CANCELFULLSCREEN:"cancelfullscreen"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r(this,e);var n={src:""};this.option=Object.assign({},n,t),this.source_=null,this.createSource_()}return o(e,[{key:"createSource_",value:function(){(this.source_=document.createElement("source")).setAttribute("src",this.option.src)}},{key:"getSource",value:function(){return this.source_}}]),e}();t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=l(n(5)),r=l(n(3)),i=l(n(0)),u=l(n(14)),a=l(n(15)),s=l(n(16));function l(e){return e&&e.__esModule?e:{default:e}}t.default={Svideo:o.default,VideoSource:r.default,Control:i.default,NextControl:u.default,FullScreenControl:a.default,Dbspeen:s.default}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=c(n(1)),i=c(n(11)),u=c(n(12)),a=c(n(3)),s=c(n(0)),l=c(n(2));function c(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var v=function(e){function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};f(this,t);var o={target:null,source:null,autoplay:!1,mode:i.default.PC,currentTime:0,loop:!1,muted:!1,playbackRate:1,poster:"",volume:1,leftControls:[],rightControls:[]};o.target=e;var r=d(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.option=Object.assign({},o,n),r.video_=null,r.init(),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"init",value:function(){var e=this,t=this.option.target,n=this.option.source,o=this.videoTarget_=null,r=this.videoSource_=null;switch(o="string"==typeof t?document.getElementById(t):t,r=n instanceof a.default?n:new a.default({src:n}),this.option.mode){case i.default.PC:this.video_=new u.default({target:o,source:r,autoplay:this.option.autoplay,currentTime:this.option.currentTime,loop:this.option.loop,muted:this.option.muted,playbackRate:this.option.playbackRate,poster:this.option.poster,volume:this.option.volume,leftControls:this.option.leftControls,rightControls:this.option.rightControls});break;case i.default.MB:}this.video_.ontimeupdate_=function(t){e.dispatchEvent(l.default.CHANGE)},this.video_.onready_=function(){e.dispatchEvent(l.default.READY)}}},{key:"getContainer",value:function(){return this.videoTarget_}},{key:"setSource",value:function(e){var t=void 0;t=e instanceof a.default?e:new a.default({src:e}),this.removeSource(),this.video_.video_.load(),this.video_.addSource_(t)}},{key:"removeSource",value:function(){this.video_.video_.removeChild(this.video_.source_)}},{key:"play",value:function(){this.video_.play_()}},{key:"pause",value:function(){this.video_.pause_()}},{key:"getAllTime",value:function(){return this.video_.getAllTime_()}},{key:"getCurrentTime",value:function(){return this.video_.getCurrentTime_()}},{key:"setCurrentTime",value:function(e){this.video_.setCurrentTime_(e)}},{key:"isReady",value:function(){return this.video_.isReady_()}},{key:"isEnded",value:function(){return this.video_.isEnded_()}},{key:"setLoop",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.video_.setLoop_(e)}},{key:"isLoop",value:function(){return this.video_.isLoop_()}},{key:"setMuted",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.video_.setMuted_(e)}},{key:"isMuted",value:function(){return this.video_.isMuted_()}},{key:"getNetworkState",value:function(){return this.video_.getNetworkState_()}},{key:"isPlay",value:function(){return this.video_.isPlay_()}},{key:"getPlaybackRate",value:function(){return this.video_.getPlaybackRate_()}},{key:"setPlaybackRate",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.video_.setPlaybackRate_(e)}},{key:"setPoster",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.video_.setPoster_(e)}},{key:"setVolume",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.video_.setVolume_(e)}},{key:"getVolume",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.video_.getVolume_(e)}},{key:"addControlLeft",value:function(e){e instanceof s.default&&this.video_.addControlLeft_(e)}},{key:"addControlRight",value:function(e){e instanceof s.default&&this.video_.addControlRight_(e)}},{key:"fullScreen",value:function(){this.video_.fullScreen_()}},{key:"cancelFullScreen",value:function(){this.video_.cancelFullScreen_()}}]),t}(r.default);t.default=v},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();var r=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.disposed=!1}return o(e,[{key:"dispose",value:function(){this.disposed||(this.disposed=!0,this.disposeInternal())}},{key:"disposeInternal",value:function(){}}]),e}();t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();t.stopPropagation=function(e){e.stopPropagation()},t.preventDefault=function(e){e.preventDefault()};var r=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.propagationStopped,this.type=t,this.target=null}return o(e,[{key:"preventDefault",value:function(){this.propagationStopped=!0}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}}]),e}();t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TRUE=function(){return!0},t.FALSE=function(){return!1},t.VOID=function(){},t.memoizeOne=function(e){var t=!1,n=void 0,r=void 0,i=void 0;return function(){var u=Array.prototype.slice.call(arguments);return t&&this===i&&(0,o.equals)(u,r)||(t=!0,i=this,r=u,n=e.apply(this,arguments)),n}};var o=n(9)},function(e,t,n){"use strict";function o(e,t){return e>t?1:e<t?-1:0}Object.defineProperty(t,"__esModule",{value:!0}),t.binarySearch=function(e,t,n){var r=void 0,i=void 0,u=n||o,a=0,s=e.length,l=!1;for(;a<s;)r=a+(s-a>>1),(i=Number(u(e[r],t)))<0?a=r+1:(s=r,l=!i);return l?a:~a},t.numberSafeCompareFunction=o,t.includes=function(e,t){return e.indexOf(t)>=0},t.linearFindNearest=function(e,t,n){var o=e.length;if(e[0]<=t)return 0;if(t<=e[o-1])return o-1;var r=void 0;if(n>0){for(r=1;r<o;++r)if(e[r]<t)return r-1}else if(n<0){for(r=1;r<o;++r)if(e[r]<=t)return r}else for(r=1;r<o;++r){if(e[r]==t)return r;if(e[r]<t)return e[r-1]-t<t-e[r]?r-1:r}return o-1},t.reverseSubArray=function(e,t,n){for(;t<n;){var o=e[t];e[t]=e[n],e[n]=o,++t,--n}},t.extend=function(e,t){for(var n=Array.isArray(t)?t:[t],o=n.length,r=0;r<o;r++)e[e.length]=n[r]},t.remove=function(e,t){var n=e.indexOf(t),o=n>-1;o&&e.splice(n,1);return o},t.find=function(e,t){for(var n=e.length>>>0,o=void 0,r=0;r<n;r++)if(o=e[r],t(o,r,e))return o;return null},t.equals=function(e,t){var n=e.length;if(n!==t.length)return!1;for(var o=0;o<n;o++)if(e[o]!==t[o])return!1;return!0},t.stableSort=function(e,t){var n=e.length,o=Array(e.length),r=void 0;for(r=0;r<n;r++)o[r]={index:r,value:e[r]};for(o.sort((function(e,n){return t(e.value,n.value)||e.index-n.index})),r=0;r<e.length;r++)e[r]=o[r].value},t.findIndex=function(e,t){var n=void 0;return e.every((function(o,r){return n=r,!t(o,r,e)}))?-1:n},t.isSorted=function(e,t,n){var r=t||o;return e.every((function(t,o){if(0===o)return!0;var i=r(e[o-1],t);return!(i>0||n&&0===i)}))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clear=function(e){for(var t in e)delete e[t]},t.isEmpty=function(e){var t=void 0;for(t in e)return!1;return!t};t.assign="function"==typeof Object.assign?Object.assign:function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(e),o=1,r=arguments.length;o<r;++o){var i=arguments[o];if(null!=i)for(var u in i)i.hasOwnProperty(u)&&(n[u]=i[u])}return n};t.getValues="function"==typeof Object.values?Object.values:function(e){var t=[];for(var n in e)t.push(e[n]);return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={PC:"pc",MB:"mb"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=n(1),u=(o=i)&&o.__esModule?o:{default:o},a=n(13);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}n(17);var c=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};s(this,t);var n={target:null,source:null,autoplay:!1},o=l(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return o.option=Object.assign({},n,e),o.createElement_(),o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"createElement_",value:function(){var e=this,t=this.option.target;t.className="sv-target";var n=this.video_=document.createElement("VIDEO");n.setAttribute("width","100%"),n.setAttribute("height","100%"),n.autoplay=this.option.autoplay,n.currentTime=this.option.currentTime,n.loop=this.option.loop,n.muted=this.option.muted,n.playbackRate=this.option.playbackRate,n.poster=this.option.poster,n.volume=this.option.volume,t.appendChild(n),this.addSource_(),this.createControlContainer_(t);var o=this.option.leftControls,r=this.option.rightControls;o.length>0&&o.forEach((function(t){e.addControlLeft_(t)})),r.length>0&&r.forEach((function(t){e.addControlRight_(t)}))}},{key:"createControlContainer_",value:function(e){var t=this,n=this.control_=document.createElement("div");n.className="sv-control",e.appendChild(n);var o=document.createElement("div");o.className="sv-play-container",n.appendChild(o);var r=this.playMenu_=document.createElement("button");r.className="sv-playBtn",o.appendChild(r);var i=this.btnInner_=document.createElement("span");i.innerHTML="&#xe60f;",i.className="sv-font sv-play",r.appendChild(i);var u=this.leftControl_=document.createElement("div");u.className="sv-control-left",o.appendChild(u);var a=document.createElement("div");a.className="sv-time",o.appendChild(a);var s=this.timeStart_=document.createElement("span");s.className="sv-time-s",s.innerHTML="00:00";var l=document.createElement("span");l.className="sv-time-split",l.innerHTML="/";var c=this.timeEnd_=document.createElement("span");c.className="sv-time-e",c.innerHTML="00:00",a.appendChild(s),a.appendChild(l),a.appendChild(c);var f=this.controlRight_=document.createElement("div");f.className="sv-control-r",n.appendChild(f);var d=this.muteMenu_=document.createElement("button");d.className="showMute",f.appendChild(d);var v=this.muteInner_=document.createElement("span");v.innerHTML="&#xe753;",v.className="sv-font sv-play",d.appendChild(v);var p=this.mutePanel_=document.createElement("div");p.className="sv-mutePanel hide",d.appendChild(p);var h=this.muteNum_=document.createElement("div");h.className="sv-mute-num",h.innerHTML="100";var _=this.muteSlider_=document.createElement("div");_.className="sv-mute-slider",p.appendChild(h),p.appendChild(_);var m=this.muteSliderRange_=document.createElement("div");m.className="sv-mute-sliderRange",_.appendChild(m);var y=this.muteSliderButton_=document.createElement("button");y.className="sv-mute-button",_.appendChild(y);var b=this.progressBar_=document.createElement("div");b.className="sv-progressBar",n.appendChild(b);var g=this.cacheProgress_=document.createElement("div");g.className="sv-cacheProgress",b.appendChild(g);var k=this.progressNum_=document.createElement("div");k.className="sv-progressNum",b.appendChild(k);var C=this.progressBtn_=document.createElement("div");C.className="sv-progressBtn hide";var E=document.createElement("div");C.appendChild(E),b.appendChild(C),this.sliderRange_(y,m),this.setVolume_(this.option.volume),this.setMuteIcon_(),this.setEventDefaultControl_(),r.onclick=function(){t.isPlay_()?(t.pause_(),i.innerHTML="&#xe60f;"):(t.play_(),i.innerHTML="&#xe693;")}}},{key:"setEventDefaultControl_",value:function(){var e=this,t=this.muteMenu_,n=this.mutePanel_,o=this.progressBar_,r=this.progressBtn_;t.onmouseover=function(){n.classList.remove("hide")};var i=null;t.onmouseleave=function(){i=setTimeout((function(){n.classList.add("hide"),clearTimeout(i)}),500)},n.onmouseover=function(){n.classList.remove("hide"),clearTimeout(i)},n.onmouseleave=function(){n.classList.remove("hide")},t.onclick=function(t){e.isMuted_()?e.setMuted_(!1):e.setMuted_(!0),t.stopPropagation()},n.onclick=function(e){e.stopPropagation()},o.onmouseover=function(){o.style.height="4px",r.classList.remove("hide")},o.onmouseleave=function(){o.style.height="2px",r.classList.add("hide")},o.onclick=function(t){var n=t.clientX,o=e.getCurrentByPx_(n-12);e.setCurrentTime_(o)};var u=o.clientWidth;o.onmouseover=function(){o.style.height="4px",r.classList.remove("hide"),o.onmousedown=function(){o.offsetLeft;document.onmousemove=function(t){var n=t.clientX;if(!(n<12||n>u)){r.style.left=n-12+"px";var o=parseInt(100*((n-12)/(u-12)).toFixed(2))/100,i=e.getCurrentByPx_(u*o);e.setCurrentTime_(i)}},document.onmouseup=function(){document.onmousemove=null,document.onmouseup=null,r.classList.add("hide")}}}}},{key:"sliderRange_",value:function(e,t){var n=this;e.onmousedown=function(o){n._isCursor=!0;var r=(o||event).clientY,i=e.offsetTop;document.onmousemove=function(o){var u=o.clientY-r+i;if(!(u>50||u<0)){e.style.top=u+"px";var a=50-u;t.style.height=a+"px",n.setVolume_(a/50)}},document.onmouseup=function(){document.onmousemove=null,document.onmouseup=null,n._isCursor=!1}}}},{key:"addSource_",value:function(e){var t=this,n=this.video_;this.source_=e?e.getSource():this.option.source.getSource(),n.appendChild(this.source_),n.ontimeupdate=function(){t.ontimeupdate_(n),n.paused?t.btnInner_.innerHTML="&#xe60f;":t.btnInner_.innerHTML="&#xe693;",t.timeStart_.innerHTML=(0,a.formatSeconds)(t.getCurrentTime_()),t.setMuteIcon_();var e=t.getAllTime_();if(e>0){for(var o=0;o<n.buffered.length;o++)if(n.buffered.start(n.buffered.length-1-o)<n.currentTime){var r=n.buffered.end(n.buffered.length-1-o)/e*100+"%";t.cacheProgress_.style.width=r;break}var i=t.getCurrentTime_();t.progressNum_.style.width=i/e*100+"%";var u=t.progressBar_.clientWidth*(i/e);t.isReady_()&&(t.progressBtn_.style.left=u-12+"px")}};var o=setInterval((function(){t.video_.readyState&&(t.cacheProgress_.style.width=t.progressNum_.style.width="0%",t.progressBtn_.style.left="0px",t.timeEnd_.innerHTML=(0,a.formatSeconds)(t.getAllTime_()),t.onready_(),clearInterval(o))}))}},{key:"setMuteIcon_",value:function(){this.isMuted_()?this.muteInner_.innerHTML="&#xe63e;":this.muteInner_.innerHTML="&#xe753;"}},{key:"ontimeupdate_",value:function(){}},{key:"onready_",value:function(){}},{key:"play_",value:function(){this.video_.play()}},{key:"pause_",value:function(){this.video_.pause()}},{key:"getAllTime_",value:function(){return this.video_.duration}},{key:"getCurrentTime_",value:function(){return this.video_.currentTime}},{key:"setCurrentTime_",value:function(e){this.video_.currentTime=e;var t=this.getAllTime_(),n=e,o=this.progressBar_.clientWidth*(n/t);this.isReady_()&&(this.progressBtn_.style.left=o-12+"px")}},{key:"getCurrentByPx_",value:function(e){return this.getAllTime_()*(e/this.progressBar_.clientWidth)}},{key:"isEnded_",value:function(){return this.video_.ended}},{key:"setLoop_",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.video_.loop=e}},{key:"isLoop_",value:function(){return this.video_.loop}},{key:"setMuted_",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.video_.muted=e,this.setMuteIcon_()}},{key:"isMuted_",value:function(){return this.video_.muted}},{key:"getNetworkState_",value:function(){return this.video_.networkState}},{key:"isPlay_",value:function(){return!this.video_.paused}},{key:"getPlaybackRate_",value:function(){return this.video_.playbackRate}},{key:"setPlaybackRate_",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.video_.playbackRate=e}},{key:"setPoster_",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.video_.poster=e}},{key:"setVolume_",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.video_.volume=e,this.muteNum_.innerHTML=parseInt(100*e),this.muteSliderRange_.style.height=100*e/2+"px",this.muteSliderButton_.style.top=50-100*e/2+"px",e>0?this.setMuted_(!1):this.setMuted_(!0)}},{key:"getVolume_",value:function(){return this.video_.volume}},{key:"isReady_",value:function(){return 4===this.video_.readyState}},{key:"addControlLeft_",value:function(e){this.leftControl_.appendChild(e.init_(this))}},{key:"addControlRight_",value:function(e){this.controlRight_.appendChild(e.init_(this))}},{key:"fullScreen_",value:function(){var e=document.documentElement;e.requestFullScreen?e.requestFullScreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullScreen&&e.webkitRequestFullScreen(),this.option.target.classList.add("sv-full-screen")}},{key:"cancelFullScreen_",value:function(){var e=document;e.exitFullscreen?e.exitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.webkitCancelFullScreen&&e.webkitCancelFullScreen(),this.option.target.classList.remove("sv-full-screen")}}]),t}(u.default);t.default=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.formatSeconds=function(e){var t=parseInt(e),n=0,o=0;t>60&&(n=parseInt(t/60),t=parseInt(t%60),n>60&&(o=parseInt(n/60),n=parseInt(n%60)));var r=String(parseInt(t));return r=t<10>0?"0"+parseInt(t):String(parseInt(t)),r=n<10>0?"0"+parseInt(n)+":"+r:String(parseInt(n))+":"+r,o>0&&(r=String(parseInt(o))+":"+r),r}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=u(n(0)),i=u(n(2));function u(e){return e&&e.__esModule?e:{default:e}}var a=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"create_",value:function(){var e=this,t=document.createElement("button");t.className="sv-nextBtn sv-font sv-next",t.innerHTML="&#xe67d;",this.element_.appendChild(t),t.onclick=function(){e.dispatchEvent(i.default.CLICK)}}}]),t}(r.default);t.default=a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=u(n(0)),i=u(n(2));function u(e){return e&&e.__esModule?e:{default:e}}var a=function(e){function t(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var e=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.isFull_=!1,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"create_",value:function(){var e=this,t=document.createElement("button");t.className="sv-nextBtn sv-font sv-fullScreen",t.innerHTML="&#xe6cc;",this.element_.appendChild(t),t.onclick=function(){e.isFull_?(e.video_.cancelFullScreen_(),t.innerHTML="&#xe6cc;",e.dispatchEvent(i.default.CANCELFULLSCREEN),e.isFull_=!1):(e.video_.fullScreen_(),t.innerHTML="&#xe71f;",e.dispatchEvent(i.default.FULLSCREEN),e.isFull_=!0)}}}]),t}(r.default);t.default=a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=n(0);function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var s=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};u(this,t);var n={speeds:["0.5","1.0","1.25","1.5","2.0"]},o=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return o.option=Object.assign({},n,e),o.active_="1.0",o.activeLi_=null,o.icon_={"1.0":"&#xe752;",.5:"&#xe754;",1.25:"&#xe757;",1.5:"&#xe758;","2.0":"&#xe759;"},o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"create_",value:function(){var e=this,t=this.speed_=document.createElement("button");t.className="sv-speedBtn sv-font sv-next";var n=this.span_=document.createElement("span");t.appendChild(n),n.innerHTML=this.icon_[this.active_],this.element_.appendChild(t);var o=document.createElement("div");o.className="sv-speed-btn hide",t.appendChild(o);var r=document.createElement("ul");this.option.speeds.forEach((function(t){var o=document.createElement("li");o.setAttribute("id",t),e.active_===t&&(o.className="sv-active",e.activeLi_=o),o.innerHTML=t+"X",o.onclick=function(){e.video_.setPlaybackRate_(parseInt(t)),e.active_=t,n.innerHTML=e.icon_[e.active_],e.activeLi_.classList.remove("sv-active"),o.className="sv-active",e.activeLi_=o},r.appendChild(o)})),o.appendChild(r),t.onmouseover=function(){o.classList.remove("hide")};var i=null;t.onmouseleave=function(){i=setTimeout((function(){o.classList.add("hide"),clearTimeout(i)}),500)},o.onmouseover=function(){o.classList.remove("hide"),clearTimeout(i)},o.onmouseleave=function(){o.classList.remove("hide")}}},{key:"setSpeed",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1.0";if(this.option.speeds.indexOf(e)>=0){this.active_=e,this.span_.innerHTML=this.icon_[this.active_];var t=document.getElementById(e);null!==this.activeLi_&&this.activeLi_.classList.remove("sv-active"),this.activeLi_=t,this.video_.setPlaybackRate_(parseInt(e))}}}]),t}(((o=i)&&o.__esModule?o:{default:o}).default);t.default=s},function(e,t){},,,,,function(e,t){}]).default}));