"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[806],{98852:function(e,t,r){r.r(t),r.d(t,{default:function(){return g}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main"},[e._m(0),t("a-modal",{staticClass:"login-modal my-login-modal",staticStyle:{width:"300px !important"},attrs:{mask:!1,maskClosable:!1,centered:"",width:"400px","show-close":!1,modal:!1,footer:null,closable:!1,keyboard:!1},model:{value:e.centerDialogVisible,callback:function(t){e.centerDialogVisible=t},expression:"centerDialogVisible"}},[t("a-spin",{attrs:{spinning:e.loading}},[t("div",{staticClass:"logo"},[t("img",{attrs:{src:r(33153),alt:"logo"}}),t("h1",{staticClass:"logo-text textover1",staticStyle:{color:"red"}},[e._v("遥遥领先")])]),t("a-form",{ref:"formLogin",staticClass:"user-layout-login",attrs:{id:"formLogin",form:e.form},on:{submit:e.handleSubmit}},[e.isLoginError?t("a-alert",{staticStyle:{"margin-bottom":"24px"},attrs:{type:"error",showIcon:"",message:e.errdesp}}):e._e(),t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["account",{rules:[{required:!0,message:e.$t("user.userName.required")},{validator:e.handleUsernameOrEmail}],validateTrigger:"change"}],expression:"[\n              'account',\n              {\n                rules: [\n                  { required: true, message: $t('user.userName.required') },\n                  { validator: handleUsernameOrEmail }\n                ],\n                validateTrigger: 'change'\n              }\n            ]"}],attrs:{size:"large",type:"text",placeholder:e.$t("user.login.username.placeholder")}},[t("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"user"},slot:"prefix"})],1)],1),t("a-form-item",[t("a-input-password",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{required:!0,message:e.$t("user.password.required")}],validateTrigger:"blur"}],expression:"[\n              'password',\n              { rules: [{ required: true, message: $t('user.password.required') }], validateTrigger: 'blur' }\n            ]"}],attrs:{size:"large",placeholder:e.$t("user.login.password.placeholder")}},[t("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["yzcode",{rules:[{required:!0,message:"请输入右侧验证码"}],initialValue:""}],expression:"['yzcode', { rules: [{ required: true, message: '请输入右侧验证码' }], initialValue: '' }]"}],staticClass:"captch-img",attrs:{placeholder:"请输入右侧验证码",size:"large",autocomplete:"off"}},[t("div",{attrs:{slot:"addonAfter"},slot:"addonAfter"},[t("img",{staticStyle:{width:"108px",cursor:"pointer"},attrs:{src:e.captchPath},on:{click:e.getCaptch}})])])],1),t("a-form-item",[t("a-checkbox",{directives:[{name:"decorator",rawName:"v-decorator",value:["rememberMe",{valuePropName:"checked"}],expression:"['rememberMe', { valuePropName: 'checked' }]"}]},[e._v(e._s(e.$t("user.login.remember-me")))]),t("router-link",{staticClass:"forge-password",staticStyle:{float:"right"},attrs:{to:{name:"recover",params:{user:"aaa"}}}},[e._v(e._s(e.$t("user.login.forgot-password")))])],1),t("a-form-item",{staticStyle:{"margin-top":"24px"}},[t("a-button",{staticClass:"login-button",attrs:{size:"large",type:"primary",htmlType:"submit",loading:e.state.loginBtn,disabled:e.state.loginBtn}},[e._v(e._s(e.$t("user.login.login")))])],1),t("a-form-item",[t("a-alert",{attrs:{description:"本软件仅供参考，请勿用于其他非法用途！",type:"warning","show-icon":""}})],1)],1)],1)],1)],1)},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"tip-container"},[t("h1",{staticStyle:{color:"#fff","font-size":"21px"}},[e._v("尊重智慧结晶，拒绝盗版软件")]),t("p",{staticStyle:{color:"yellow","margin-top":"15px"}},[e._v("注意甄别仿制盗版，请通过官方渠道获取正版软件，享受完整功能、安全保障及持续更新。")])])}],o=r(76338),i=(r(26099),r(27495),r(23500),r(95353)),n=r(67569),l=r(55499),c=r(81625),u={components:{},data:function(){return{customActiveKey:"tab1",loginBtn:!1,centerDialogVisible:!0,loginType:0,isLoginError:!1,errdesp:"",form:this.$form.createForm(this),captchPath:"",loading:!1,state:{time:60,loginBtn:!1,loginType:0,smsSendBtn:!1}}},created:function(){this.getCaptch()},methods:(0,o.A)((0,o.A)({},(0,i.i0)(["Login","Logout"])),{},{getCaptch:function(){this.captchPath="/red/users/imgverify?t="+Math.random()},handleUsernameOrEmail:function(e,t,r){var s=this.state,a=/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;a.test(t)?s.loginType=0:s.loginType=1,r()},handleTabClick:function(e){this.customActiveKey=e},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields,s=this.state,a=this.Login;s.loginBtn=!0;var i=["account","password","yzcode"];this.loading=!0,r(i,{force:!0},(function(e,r){if(e)setTimeout((function(){s.loginBtn=!1,t.loading=!1}),600);else{var i=(0,o.A)({},r);delete i.account,i[s.loginType?"account":"email"]=r.account,i["yzmsrc"]=t.captchPath,a(i).then((function(e){t.loginSuccess(e),t.loading=!1})).catch((function(e){console.log("err =",e),t.requestFailed(e)})).finally((function(){s.loginBtn=!1,t.loading=!1}))}}))},loginSuccess:function(e){var t=this;console.log(e),l.A.dispatch("GenerateRoutes",(0,o.A)({token:!0},e)).then((function(){(0,c.d)(),l.A.getters.addRouters.forEach((function(e){t.$router.addRoute(e)})),t.$router.push({path:"/"})})),setTimeout((function(){t.$notification.success({message:"欢迎",description:"".concat((0,n.Z$)(),"，欢迎回来")})}),1e3),this.isLoginError=!1},requestFailed:function(e){this.isLoginError=!0,this.errdesp=((e.response||{}).data||{}).error||"请求出现错误，请稍后再试",this.$notification["error"]({message:"错误",description:((e.response||{}).data||{}).error||"请求出现错误，请稍后再试",duration:4})}})},d=u,p=r(81656),m=(0,p.A)(d,s,a,!1,null,"404ea2ce",null),g=m.exports},71194:function(e,t,r){r.r(t),r.d(t,{default:function(){return v}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"main user-layout-register"},[t("h3",[t("span",[e._v(e._s(e.$t("user.register.register")))])]),t("a-form",{ref:"formRegister",attrs:{form:e.form,id:"formRegister"}},[t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["email",{rules:[{required:!0,type:"email",message:e.$t("user.email.required")}],validateTrigger:["change","blur"]}],expression:"['email', {rules: [{ required: true, type: 'email', message: $t('user.email.required') }], validateTrigger: ['change', 'blur']}]"}],attrs:{size:"large",type:"text",placeholder:e.$t("user.register.email.placeholder")}})],1),t("a-popover",{attrs:{placement:"rightTop",trigger:["focus"],getPopupContainer:function(e){return e.parentElement}},model:{value:e.state.passwordLevelChecked,callback:function(t){e.$set(e.state,"passwordLevelChecked",t)},expression:"state.passwordLevelChecked"}},[t("template",{slot:"content"},[t("div",{style:{width:"240px"}},[t("div",{class:["user-register",e.passwordLevelClass]},[e._v(e._s(e.$t(e.passwordLevelName)))]),t("a-progress",{attrs:{percent:e.state.percent,showInfo:!1,strokeColor:e.passwordLevelColor}}),t("div",{staticStyle:{"margin-top":"10px"}},[t("span",[e._v(e._s(e.$t("user.register.password.popover-message"))+" ")])])],1)]),t("a-form-item",[t("a-input-password",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{required:!0,message:e.$t("user.password.required")},{validator:this.handlePasswordLevel}],validateTrigger:["change","blur"]}],expression:"['password', {rules: [{ required: true, message: $t('user.password.required') }, { validator: this.handlePasswordLevel }], validateTrigger: ['change', 'blur']}]"}],attrs:{size:"large",placeholder:e.$t("user.register.password.placeholder")},on:{click:e.handlePasswordInputClick}})],1)],2),t("a-form-item",[t("a-input-password",{directives:[{name:"decorator",rawName:"v-decorator",value:["password2",{rules:[{required:!0,message:e.$t("user.password.required")},{validator:this.handlePasswordCheck}],validateTrigger:["change","blur"]}],expression:"['password2', {rules: [{ required: true, message: $t('user.password.required') }, { validator: this.handlePasswordCheck }], validateTrigger: ['change', 'blur']}]"}],attrs:{size:"large",placeholder:e.$t("user.register.confirm-password.placeholder")}})],1),t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["mobile",{rules:[{required:!0,message:e.$t("user.phone-number.required"),pattern:/^1[3456789]\d{9}$/},{validator:this.handlePhoneCheck}],validateTrigger:["change","blur"]}],expression:"['mobile', {rules: [{ required: true, message: $t('user.phone-number.required'), pattern: /^1[3456789]\\d{9}$/ }, { validator: this.handlePhoneCheck } ], validateTrigger: ['change', 'blur'] }]"}],attrs:{size:"large",placeholder:e.$t("user.login.mobile.placeholder")}},[t("a-select",{attrs:{slot:"addonBefore",size:"large",defaultValue:"+86"},slot:"addonBefore"},[t("a-select-option",{attrs:{value:"+86"}},[e._v("+86")]),t("a-select-option",{attrs:{value:"+87"}},[e._v("+87")])],1)],1)],1),t("a-row",{attrs:{gutter:16}},[t("a-col",{staticClass:"gutter-row",attrs:{span:16}},[t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["captcha",{rules:[{required:!0,message:"请输入验证码"}],validateTrigger:"blur"}],expression:"['captcha', {rules: [{ required: true, message: '请输入验证码' }], validateTrigger: 'blur'}]"}],attrs:{size:"large",type:"text",placeholder:e.$t("user.login.mobile.verification-code.placeholder")}},[t("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),t("a-col",{staticClass:"gutter-row",attrs:{span:8}},[t("a-button",{staticClass:"getCaptcha",attrs:{size:"large",disabled:e.state.smsSendBtn},domProps:{textContent:e._s(!e.state.smsSendBtn&&e.$t("user.register.get-verification-code")||e.state.time+" s")},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.getCaptcha.apply(null,arguments)}}})],1)],1),t("a-form-item",[t("a-button",{staticClass:"register-button",attrs:{size:"large",type:"primary",htmlType:"submit",loading:e.registerBtn,disabled:e.registerBtn},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[e._v(e._s(e.$t("user.register.register"))+" ")]),t("router-link",{staticClass:"login",attrs:{to:{name:"login"}}},[e._v(e._s(e.$t("user.register.sign-in")))])],1)],1)],1)},a=[],o=r(76338),i=(r(42762),r(505)),n=r(99547),l=r(67569),c={0:"user.password.strength.short",1:"user.password.strength.low",2:"user.password.strength.medium",3:"user.password.strength.strong"},u={0:"error",1:"error",2:"warning",3:"success"},d={0:"#ff0000",1:"#ff0000",2:"#ff7e05",3:"#52c41a"},p={name:"Register",components:{},mixins:[n.w],data:function(){return{form:this.$form.createForm(this),state:{time:60,level:0,smsSendBtn:!1,passwordLevel:0,passwordLevelChecked:!1,percent:10,progressColor:"#FF0000"},registerBtn:!1}},computed:{passwordLevelClass:function(){return u[this.state.passwordLevel]},passwordLevelName:function(){return c[this.state.passwordLevel]},passwordLevelColor:function(){return d[this.state.passwordLevel]}},methods:{handlePasswordLevel:function(e,t,r){if(!t)return r();console.log("scorePassword ; ",(0,l.Av)(t)),t.length>=6?((0,l.Av)(t)>=30&&(this.state.level=1),(0,l.Av)(t)>=60&&(this.state.level=2),(0,l.Av)(t)>=80&&(this.state.level=3)):(this.state.level=0,r(new Error(this.$t("user.password.strength.msg")))),this.state.passwordLevel=this.state.level,this.state.percent=33*this.state.level,r()},handlePasswordCheck:function(e,t,r){var s=this.form.getFieldValue("password");void 0===t&&r(new Error(this.$t("user.password.required"))),t&&s&&t.trim()!==s.trim()&&r(new Error(this.$t("user.password.twice.msg"))),r()},handlePhoneCheck:function(e,t,r){console.log("handlePhoneCheck, rule:",e),console.log("handlePhoneCheck, value",t),console.log("handlePhoneCheck, callback",r),r()},handlePasswordInputClick:function(){this.isMobile?this.state.passwordLevelChecked=!1:this.state.passwordLevelChecked=!0},handleSubmit:function(){var e=this.form.validateFields,t=this.state,r=this.$router;e({force:!0},(function(e,s){e||(t.passwordLevelChecked=!1,r.push({name:"registerResult",params:(0,o.A)({},s)}))}))},getCaptcha:function(e){var t=this;e.preventDefault();var r=this.form.validateFields,s=this.state,a=this.$message,o=this.$notification;r(["mobile"],{force:!0},(function(e,r){if(!e){s.smsSendBtn=!0;var n=window.setInterval((function(){s.time--<=0&&(s.time=60,s.smsSendBtn=!1,window.clearInterval(n))}),1e3),l=a.loading("验证码发送中..",0);(0,i.getSmsCaptcha)({mobile:r.mobile}).then((function(e){setTimeout(l,2500),o["success"]({message:"提示",description:"验证码获取成功，您的验证码为："+e.result.captcha,duration:8})})).catch((function(e){setTimeout(l,1),clearInterval(n),s.time=60,s.smsSendBtn=!1,t.requestFailed(e)}))}}))},requestFailed:function(e){this.$notification["error"]({message:"错误",description:((e.response||{}).data||{}).message||"请求出现错误，请稍后再试",duration:4}),this.registerBtn=!1}},watch:{"state.passwordLevel":function(e){console.log(e)}}},m=p,g=r(81656),h=(0,g.A)(m,s,a,!1,null,"0c6ae390",null),v=h.exports},2275:function(e,t,r){r.r(t),r.d(t,{default:function(){return c}});r(52675),r(89463);var s=function(){var e=this,t=e._self._c;return t("a-result",{attrs:{isSuccess:!0,content:!1,title:e.email,"sub-title":e.description},scopedSlots:e._u([{key:"extra",fn:function(){return[t("a-button",{attrs:{size:"large",type:"primary"}},[e._v("查看邮箱")]),t("a-button",{staticStyle:{"margin-left":"8px"},attrs:{size:"large"},on:{click:e.goHomeHandle}},[e._v("返回首页")])]},proxy:!0}])})},a=[],o={name:"RegisterResult",data:function(){return{description:"激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。",form:{}}},computed:{email:function(){var e=this.form&&this.form.email||"xxx";return"你的账户：".concat(e," 注册成功")}},created:function(){this.form=this.$route.params},methods:{goHomeHandle:function(){this.$router.push({name:"login"})}}},i=o,n=r(81656),l=(0,n.A)(i,s,a,!1,null,"4a6c53ae",null),c=l.exports}}]);