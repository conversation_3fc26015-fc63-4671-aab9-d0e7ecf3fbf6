"use strict";(self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[]).push([[77,98,254,345,376,418,438,533,606,729,782,802,924,980],{91758:function(e,s,t){t.r(s),t.d(s,{default:function(){return y}});var a=t(76338),r=t(81966),i=r.A,o=i,n=t(3508),c=t.n(n),l=t(12610),u=t(53050),d=t(18749),m=t(28666),p=t(12089),f=t(98031),g=t(29780),h=t(21970),b={antLocale:o,momentName:"eu",momentLocale:c()},y=(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({message:"-","layouts.usermenu.dialog.title":"Message","layouts.usermenu.dialog.content":"Are you sure you would like to logout?","layouts.userLayout.title":"Ant Design is the most influential web design specification in Xihu district"},b),l["default"]),u["default"]),d["default"]),m["default"]),p["default"]),f["default"]),g["default"]),h["default"])},21970:function(e,s,t){t.r(s);var a=t(76338),r=t(36748);s["default"]=(0,a.A)({},r["default"])},36748:function(e,s,t){t.r(s),s["default"]={"account.settings.menuMap.basic":"Basic Settings","account.settings.menuMap.security":"Security Settings","account.settings.menuMap.custom":"Custom Settings","account.settings.menuMap.binding":"Account Binding","account.settings.menuMap.notification":"New Message Notification","account.settings.basic.avatar":"Avatar","account.settings.basic.change-avatar":"Change avatar","account.settings.basic.email":"Email","account.settings.basic.email-message":"Please input your email!","account.settings.basic.nickname":"Nickname","account.settings.basic.nickname-message":"Please input your Nickname!","account.settings.basic.profile":"Personal profile","account.settings.basic.profile-message":"Please input your personal profile!","account.settings.basic.profile-placeholder":"Brief introduction to yourself","account.settings.basic.country":"Country/Region","account.settings.basic.country-message":"Please input your country!","account.settings.basic.geographic":"Province or city","account.settings.basic.geographic-message":"Please input your geographic info!","account.settings.basic.address":"Street Address","account.settings.basic.address-message":"Please input your address!","account.settings.basic.phone":"Phone Number","account.settings.basic.phone-message":"Please input your phone!","account.settings.basic.update":"Update Information","account.settings.basic.update.success":"Update basic information successfully","account.settings.security.strong":"Strong","account.settings.security.medium":"Medium","account.settings.security.weak":"Weak","account.settings.security.password":"Account Password","account.settings.security.password-description":"Current password strength：","account.settings.security.phone":"Security Phone","account.settings.security.phone-description":"Bound phone：","account.settings.security.question":"Security Question","account.settings.security.question-description":"The security question is not set, and the security policy can effectively protect the account security","account.settings.security.email":"Backup Email","account.settings.security.email-description":"Bound Email：","account.settings.security.mfa":"MFA Device","account.settings.security.mfa-description":"Unbound MFA device, after binding, can be confirmed twice","account.settings.security.modify":"Modify","account.settings.security.set":"Set","account.settings.security.bind":"Bind","account.settings.binding.taobao":"Binding Taobao","account.settings.binding.taobao-description":"Currently unbound Taobao account","account.settings.binding.alipay":"Binding Alipay","account.settings.binding.alipay-description":"Currently unbound Alipay account","account.settings.binding.dingding":"Binding DingTalk","account.settings.binding.dingding-description":"Currently unbound DingTalk account","account.settings.binding.bind":"Bind","account.settings.notification.password":"Account Password","account.settings.notification.password-description":"Messages from other users will be notified in the form of a station letter","account.settings.notification.messages":"System Messages","account.settings.notification.messages-description":"System messages will be notified in the form of a station letter","account.settings.notification.todo":"To-do Notification","account.settings.notification.todo-description":"The to-do list will be notified in the form of a letter from the station","account.settings.settings.open":"Open","account.settings.settings.close":"Close"}},12089:function(e,s,t){t.r(s);var a=t(76338),r=t(77062);s["default"]=(0,a.A)({},r["default"])},77062:function(e,s,t){t.r(s),s["default"]={"dashboard.analysis.test":"Gongzhuan No.{no} shop","dashboard.analysis.introduce":"Introduce","dashboard.analysis.total-sales":"Total Sales","dashboard.analysis.day-sales":"Daily Sales","dashboard.analysis.visits":"Visits","dashboard.analysis.visits-trend":"Visits Trend","dashboard.analysis.visits-ranking":"Visits Ranking","dashboard.analysis.day-visits":"Daily Visits","dashboard.analysis.week":"WoW Change","dashboard.analysis.day":"DoD Change","dashboard.analysis.payments":"Payments","dashboard.analysis.conversion-rate":"Conversion Rate","dashboard.analysis.operational-effect":"Operational Effect","dashboard.analysis.sales-trend":"Stores Sales Trend","dashboard.analysis.sales-ranking":"Sales Ranking","dashboard.analysis.all-year":"All Year","dashboard.analysis.all-month":"All Month","dashboard.analysis.all-week":"All Week","dashboard.analysis.all-day":"All day","dashboard.analysis.search-users":"Search Users","dashboard.analysis.per-capita-search":"Per Capita Search","dashboard.analysis.online-top-search":"Online Top Search","dashboard.analysis.the-proportion-of-sales":"The Proportion Of Sales","dashboard.analysis.dropdown-option-one":"Operation one","dashboard.analysis.dropdown-option-two":"Operation two","dashboard.analysis.channel.all":"ALL","dashboard.analysis.channel.online":"Online","dashboard.analysis.channel.stores":"Stores","dashboard.analysis.sales":"Sales","dashboard.analysis.traffic":"Traffic","dashboard.analysis.table.rank":"Rank","dashboard.analysis.table.search-keyword":"Keyword","dashboard.analysis.table.users":"Users","dashboard.analysis.table.weekly-range":"Weekly Range"}},98031:function(e,s,t){t.r(s);var a=t(76338),r=t(14932);s["default"]=(0,a.A)({},r["default"])},14932:function(e,s,t){t.r(s),s["default"]={"form.basic-form.basic.title":"Basic form","form.basic-form.basic.description":"Form pages are used to collect or verify information to users, and basic forms are common in scenarios where there are fewer data items.","form.basic-form.title.label":"Title","form.basic-form.title.placeholder":"Give the target a name","form.basic-form.title.required":"Please enter a title","form.basic-form.date.label":"Start and end date","form.basic-form.placeholder.start":"Start date","form.basic-form.placeholder.end":"End date","form.basic-form.date.required":"Please select the start and end date","form.basic-form.goal.label":"Goal description","form.basic-form.goal.placeholder":"Please enter your work goals","form.basic-form.goal.required":"Please enter a description of the goal","form.basic-form.standard.label":"Metrics","form.basic-form.standard.placeholder":"Please enter a metric","form.basic-form.standard.required":"Please enter a metric","form.basic-form.client.label":"Client","form.basic-form.label.tooltip":"Target service object","form.basic-form.client.placeholder":"Please describe your customer service, internal customers directly @ Name / job number","form.basic-form.client.required":"Please describe the customers you serve","form.basic-form.invites.label":"Inviting critics","form.basic-form.invites.placeholder":"Please direct @ Name / job number, you can invite up to 5 people","form.basic-form.weight.label":"Weight","form.basic-form.weight.placeholder":"Please enter weight","form.basic-form.public.label":"Target disclosure","form.basic-form.label.help":"Customers and invitees are shared by default","form.basic-form.radio.public":"Public","form.basic-form.radio.partially-public":"Partially public","form.basic-form.radio.private":"Private","form.basic-form.publicUsers.placeholder":"Open to","form.basic-form.option.A":"Colleague A","form.basic-form.option.B":"Colleague B","form.basic-form.option.C":"Colleague C","form.basic-form.email.required":"Please enter your email!","form.basic-form.email.wrong-format":"The email address is in the wrong format!","form.basic-form.userName.required":"Please enter your userName!","form.basic-form.password.required":"Please enter your password!","form.basic-form.password.twice":"The passwords entered twice do not match!","form.basic-form.strength.msg":"Please enter at least 6 characters and don't use passwords that are easy to guess.","form.basic-form.strength.strong":"Strength: strong","form.basic-form.strength.medium":"Strength: medium","form.basic-form.strength.short":"Strength: too short","form.basic-form.confirm-password.required":"Please confirm your password!","form.basic-form.phone-number.required":"Please enter your phone number!","form.basic-form.phone-number.wrong-format":"Malformed phone number!","form.basic-form.verification-code.required":"Please enter the verification code!","form.basic-form.form.get-captcha":"Get Captcha","form.basic-form.captcha.second":"sec","form.basic-form.form.optional":" (optional) ","form.basic-form.form.submit":"Submit","form.basic-form.form.save":"Save","form.basic-form.email.placeholder":"Email","form.basic-form.password.placeholder":"Password","form.basic-form.confirm-password.placeholder":"Confirm password","form.basic-form.phone-number.placeholder":"Phone number","form.basic-form.verification-code.placeholder":"Verification code"}},12610:function(e,s,t){t.r(s),s["default"]={submit:"Submit",save:"Save","submit.ok":"Submit successfully","save.ok":"Saved successfully"}},53050:function(e,s,t){t.r(s),s["default"]={"menu.welcome":"Welcome","menu.home":"Home","menu.dashboard":"Dashboard","menu.dashboard.analysis":"Analysis","menu.dashboard.monitor":"Monitor","menu.dashboard.workplace":"Workplace11111111111","menu.form":"Form","menu.form.basic-form":"Basic Form","menu.form.step-form":"Step Form","menu.form.step-form.info":"Step Form(write transfer information)","menu.form.step-form.confirm":"Step Form(confirm transfer information)","menu.form.step-form.result":"Step Form(finished)","menu.form.advanced-form":"Advanced Form","menu.list":"List","menu.list.table-list":"Search Table","menu.list.basic-list":"Basic List","menu.list.card-list":"Card List","menu.list.search-list":"Search List","menu.list.search-list.articles":"Search List(articles)","menu.list.search-list.projects":"Search List(projects)","menu.list.search-list.applications":"Search List(applications)","menu.profile":"Profile","menu.profile.basic":"Basic Profile","menu.profile.advanced":"Advanced Profile","menu.result":"Result","menu.result.success":"Success","menu.result.fail":"Fail","menu.exception":"Exception","menu.exception.not-permission":"403","menu.exception.not-find":"404","menu.exception.server-error":"500","menu.exception.trigger":"Trigger","menu.account":"Account","menu.account.center":"Account Center","menu.account.settings":"Account Settings","menu.account.trigger":"Trigger Error","menu.account.logout":"Logout"}},29780:function(e,s,t){t.r(s);var a=t(76338),r=t(10088),i=t(78915);s["default"]=(0,a.A)((0,a.A)({},r["default"]),i["default"])},78915:function(e,s,t){t.r(s),s["default"]={"result.fail.error.title":"Submission Failed","result.fail.error.description":"Please check and modify the following information before resubmitting.","result.fail.error.hint-title":"The content you submitted has the following error:","result.fail.error.hint-text1":"Your account has been frozen","result.fail.error.hint-btn1":"Thaw immediately","result.fail.error.hint-text2":"Your account is not yet eligible to apply","result.fail.error.hint-btn2":"Upgrade immediately","result.fail.error.btn-text":"Return to modify"}},10088:function(e,s,t){t.r(s),s["default"]={"result.success.title":"Submission Success","result.success.description":"The submission results page is used to feed back the results of a series of operational tasks. If it is a simple operation, use the Message global prompt feedback. This text area can show a simple supplementary explanation. If there is a similar requirement for displaying “documents”, the following gray area can present more complicated content.","result.success.operate-title":"Project Name","result.success.operate-id":"Project ID","result.success.principal":"Principal","result.success.operate-time":"Effective time","result.success.step1-title":"Create project","result.success.step1-operator":"Qu Lili","result.success.step2-title":"Departmental preliminary review","result.success.step2-operator":"Zhou Maomao","result.success.step2-extra":"Urge","result.success.step3-title":"Financial review","result.success.step4-title":"Finish","result.success.btn-return":"Back List","result.success.btn-project":"View Project","result.success.btn-print":"Print"}},18749:function(e,s,t){t.r(s),s["default"]={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.light":"Light style","app.setting.pagestyle.dark":"Dark style","app.setting.pagestyle.realdark":"RealDark style","app.setting.themecolor":"Theme Color","app.setting.navigationmode":"Navigation Mode","app.setting.content-width":"Content Width","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success，please replace defaultSettings in src/config/defaultSettings.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple"}},28666:function(e,s,t){t.r(s),s["default"]={"user.login.userName":"userName","user.login.password":"password","user.login.username.placeholder":"Account: admin","user.login.password.placeholder":"password: admin or ant.design","user.login.message-invalid-credentials":"Invalid username or password（admin/ant.design）","user.login.message-invalid-verification-code":"Invalid verification code","user.login.tab-login-credentials":"Credentials","user.login.tab-login-mobile":"Mobile number","user.login.mobile.placeholder":"Mobile number","user.login.mobile.verification-code.placeholder":"Verification code","user.login.remember-me":"Remember me","user.login.forgot-password":"Forgot your password?","user.login.sign-in-with":"Sign in with","user.login.signup":"Sign up","user.login.login":"Login","user.register.register":"Register","user.register.email.placeholder":"Email","user.register.password.placeholder":"Password ","user.register.password.popover-message":"Please enter at least 6 characters. Please do not use passwords that are easy to guess. ","user.register.confirm-password.placeholder":"Confirm password","user.register.get-verification-code":"Get code","user.register.sign-in":"Already have an account?","user.register-result.msg":"Account：registered at {email}","user.register-result.activation-email":"The activation email has been sent to your email address and is valid for 24 hours. Please log in to the email in time and click on the link in the email to activate the account.","user.register-result.back-home":"Back to home","user.register-result.view-mailbox":"View mailbox","user.email.required":"Please enter your email!","user.email.wrong-format":"The email address is in the wrong format!","user.userName.required":"Please enter account name or email address","user.password.required":"Please enter your password!","user.password.twice.msg":"The passwords entered twice do not match!","user.password.strength.msg":"The password is not strong enough","user.password.strength.strong":"Strength: strong","user.password.strength.medium":"Strength: medium","user.password.strength.low":"Strength: low","user.password.strength.short":"Strength: too short","user.confirm-password.required":"Please confirm your password!","user.phone-number.required":"Please enter your phone number!","user.phone-number.wrong-format":"Please enter a valid phone number","user.verification-code.required":"Please enter the verification code!"}}}]);