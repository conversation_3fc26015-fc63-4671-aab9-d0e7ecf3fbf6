(function(){var e={98490:function(e,t,n){var r=n(71332)["default"],s=n(88498)["default"],o=["class","staticClass","style","staticStyle","attrs"];n(28706),e.exports={functional:!0,render:function(e,t){var n=t._c,i=(t._v,t.data),a=t.children,c=void 0===a?[]:a,u=i.class,l=i.staticClass,d=i.style,f=i.staticStyle,m=i.attrs,p=void 0===m?{}:m,h=s(i,o);return n("svg",r({class:["bx-analyse_svg__icon",u,l],style:[d,f],attrs:Object.assign({viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg",width:"200",height:"200"},p)},h),c.concat([n("defs"),n("path",{attrs:{d:"M85.333 512h85.334a340.736 340.736 0 0199.712-241.621 337.493 337.493 0 01108.458-72.96 346.453 346.453 0 01261.547-1.75 106.155 106.155 0 00106.283 102.998c59.136 0 106.666-47.531 106.666-106.667S805.803 85.333 746.667 85.333c-29.398 0-55.979 11.776-75.222 30.934-103.722-41.515-222.848-40.875-325.76 2.517a423.595 423.595 0 00-135.68 91.264A423.253 423.253 0 00118.7 345.685 426.88 426.88 0 0085.333 512zm741.248 133.205c-17.109 40.619-41.685 77.142-72.96 108.416s-67.797 55.851-108.458 72.96a346.453 346.453 0 01-261.547 1.75 106.155 106.155 0 00-106.283-102.998c-59.136 0-106.666 47.531-106.666 106.667s47.53 106.667 106.666 106.667c29.398 0 55.979-11.776 75.222-30.934A425.173 425.173 0 00512 938.667a425.941 425.941 0 00393.259-260.352A426.325 426.325 0 00938.667 512h-85.334a341.035 341.035 0 01-26.752 133.205z"}}),n("path",{attrs:{d:"M512 318.379c-106.752 0-193.621 86.869-193.621 193.621S405.248 705.621 512 705.621 705.621 618.752 705.621 512 618.752 318.379 512 318.379zm0 301.909c-59.69 0-108.288-48.597-108.288-108.288S452.309 403.712 512 403.712 620.288 452.309 620.288 512 571.691 620.288 512 620.288z"}})]))}}},35358:function(e,t,n){var r={"./af":25177,"./af.js":25177,"./ar":61509,"./ar-dz":41488,"./ar-dz.js":41488,"./ar-kw":58676,"./ar-kw.js":58676,"./ar-ly":42353,"./ar-ly.js":42353,"./ar-ma":24496,"./ar-ma.js":24496,"./ar-ps":6947,"./ar-ps.js":6947,"./ar-sa":82682,"./ar-sa.js":82682,"./ar-tn":89756,"./ar-tn.js":89756,"./ar.js":61509,"./az":95533,"./az.js":95533,"./be":28959,"./be.js":28959,"./bg":47777,"./bg.js":47777,"./bm":54903,"./bm.js":54903,"./bn":61290,"./bn-bd":17357,"./bn-bd.js":17357,"./bn.js":61290,"./bo":31545,"./bo.js":31545,"./br":11470,"./br.js":11470,"./bs":44429,"./bs.js":44429,"./ca":7306,"./ca.js":7306,"./cs":56464,"./cs.js":56464,"./cv":73635,"./cv.js":73635,"./cy":64226,"./cy.js":64226,"./da":93601,"./da.js":93601,"./de":77853,"./de-at":26111,"./de-at.js":26111,"./de-ch":54697,"./de-ch.js":54697,"./de.js":77853,"./dv":60708,"./dv.js":60708,"./el":54691,"./el.js":54691,"./en-au":53872,"./en-au.js":53872,"./en-ca":28298,"./en-ca.js":28298,"./en-gb":56195,"./en-gb.js":56195,"./en-ie":66584,"./en-ie.js":66584,"./en-il":65543,"./en-il.js":65543,"./en-in":9033,"./en-in.js":9033,"./en-nz":79402,"./en-nz.js":79402,"./en-sg":43004,"./en-sg.js":43004,"./eo":32934,"./eo.js":32934,"./es":97650,"./es-do":20838,"./es-do.js":20838,"./es-mx":17730,"./es-mx.js":17730,"./es-us":56575,"./es-us.js":56575,"./es.js":97650,"./et":3035,"./et.js":3035,"./eu":3508,"./eu.js":3508,"./fa":119,"./fa.js":119,"./fi":90527,"./fi.js":90527,"./fil":95995,"./fil.js":95995,"./fo":52477,"./fo.js":52477,"./fr":85498,"./fr-ca":26435,"./fr-ca.js":26435,"./fr-ch":37892,"./fr-ch.js":37892,"./fr.js":85498,"./fy":37071,"./fy.js":37071,"./ga":41734,"./ga.js":41734,"./gd":70217,"./gd.js":70217,"./gl":77329,"./gl.js":77329,"./gom-deva":32124,"./gom-deva.js":32124,"./gom-latn":93383,"./gom-latn.js":93383,"./gu":95050,"./gu.js":95050,"./he":11713,"./he.js":11713,"./hi":43861,"./hi.js":43861,"./hr":26308,"./hr.js":26308,"./hu":90609,"./hu.js":90609,"./hy-am":17160,"./hy-am.js":17160,"./id":74063,"./id.js":74063,"./is":89374,"./is.js":89374,"./it":88383,"./it-ch":21827,"./it-ch.js":21827,"./it.js":88383,"./ja":23827,"./ja.js":23827,"./jv":89722,"./jv.js":89722,"./ka":41794,"./ka.js":41794,"./kk":27088,"./kk.js":27088,"./km":96870,"./km.js":96870,"./kn":84451,"./kn.js":84451,"./ko":63164,"./ko.js":63164,"./ku":98174,"./ku-kmr":6181,"./ku-kmr.js":6181,"./ku.js":98174,"./ky":78474,"./ky.js":78474,"./lb":79680,"./lb.js":79680,"./lo":15867,"./lo.js":15867,"./lt":45766,"./lt.js":45766,"./lv":69532,"./lv.js":69532,"./me":58076,"./me.js":58076,"./mi":41848,"./mi.js":41848,"./mk":30306,"./mk.js":30306,"./ml":73739,"./ml.js":73739,"./mn":99053,"./mn.js":99053,"./mr":86169,"./mr.js":86169,"./ms":73386,"./ms-my":92297,"./ms-my.js":92297,"./ms.js":73386,"./mt":77075,"./mt.js":77075,"./my":72264,"./my.js":72264,"./nb":22274,"./nb.js":22274,"./ne":8235,"./ne.js":8235,"./nl":92572,"./nl-be":43784,"./nl-be.js":43784,"./nl.js":92572,"./nn":54566,"./nn.js":54566,"./oc-lnc":69330,"./oc-lnc.js":69330,"./pa-in":29849,"./pa-in.js":29849,"./pl":94418,"./pl.js":94418,"./pt":79834,"./pt-br":48303,"./pt-br.js":48303,"./pt.js":79834,"./ro":24457,"./ro.js":24457,"./ru":82271,"./ru.js":82271,"./sd":1221,"./sd.js":1221,"./se":33478,"./se.js":33478,"./si":17538,"./si.js":17538,"./sk":5784,"./sk.js":5784,"./sl":46637,"./sl.js":46637,"./sq":86794,"./sq.js":86794,"./sr":45719,"./sr-cyrl":3322,"./sr-cyrl.js":3322,"./sr.js":45719,"./ss":56e3,"./ss.js":56e3,"./sv":41011,"./sv.js":41011,"./sw":40748,"./sw.js":40748,"./ta":11025,"./ta.js":11025,"./te":11885,"./te.js":11885,"./tet":28861,"./tet.js":28861,"./tg":86571,"./tg.js":86571,"./th":55802,"./th.js":55802,"./tk":59527,"./tk.js":59527,"./tl-ph":29231,"./tl-ph.js":29231,"./tlh":31052,"./tlh.js":31052,"./tr":85096,"./tr.js":85096,"./tzl":79846,"./tzl.js":79846,"./tzm":81765,"./tzm-latn":97711,"./tzm-latn.js":97711,"./tzm.js":81765,"./ug-cn":48414,"./ug-cn.js":48414,"./uk":16618,"./uk.js":16618,"./ur":57777,"./ur.js":57777,"./uz":57609,"./uz-latn":72475,"./uz-latn.js":72475,"./uz.js":57609,"./vi":21135,"./vi.js":21135,"./x-pseudo":64051,"./x-pseudo.js":64051,"./yo":82218,"./yo.js":82218,"./zh-cn":52648,"./zh-cn.js":52648,"./zh-hk":1632,"./zh-hk.js":1632,"./zh-mo":31541,"./zh-mo.js":31541,"./zh-tw":50304,"./zh-tw.js":50304};function s(e){var t=o(e);return n(t)}function o(e){if(!n.o(r,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return r[e]}s.keys=function(){return Object.keys(r)},s.resolve=o,e.exports=s,s.id=35358},505:function(e,t,n){"use strict";n.d(t,{Av:function(){return d},Vp:function(){return c},ZA:function(){return l},iD:function(){return a},ri:function(){return u}});var r=n(75769),s=window,o=s.ipcRendererChannel,i={Login:"/users/login",Logout:"/users/logout",UserInfo:"/users/getme"};function a(e){return(0,r.Ay)({url:i.Login,method:"post",data:e})}function c(){return(0,r.Ay)({url:i.UserInfo,method:"get",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(){return(0,r.Ay)({url:i.Logout,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return o.OpenWin.invoke(e)}function d(e){return o.OpenWinEx.invoke(e)}},41036:function(e,t,n){"use strict";n.d(t,{$c:function(){return c},aU:function(){return o},bz:function(){return i},x$:function(){return a}});n(28706);var r=n(75769),s={user:"/users/findall",createuser:"/users/register",updatauser:"/users/update",deluser:"/users/deleteone",delmanyusers:"/users/deletemany",findone:"/users/findone"};function o(e){return(0,r.Ay)({url:s.user,method:"get",params:e})}function i(e){return(0,r.Ay)({url:"".concat(s.deluser,"/").concat(e.uuid),method:"delete"})}function a(e){return(0,r.Ay)({url:s.delmanyusers,method:"post",data:e})}function c(e){return(0,r.Ay)({url:e.uuid?"".concat(s.updatauser,"/").concat(e.uuid):s.createuser,method:e.uuid?"put":"post",data:e})}},66117:function(e,t,n){"use strict";var r=n(76338),s=(n(26099),n(43898));t.A=function(e){function t(t,n,o){var i=this;if(o=o||{},i&&i._isVue){var a=document.querySelector("body>div[type=dialog]");a||(a=document.createElement("div"),a.setAttribute("type","dialog"),document.body.appendChild(a));var c=function(e,t){if(e instanceof Function){var n=e();n instanceof Promise?n.then((function(e){e&&t()})):n&&t()}else e||t()},u=new e({data:function(){return{visible:!0}},router:i.$router,store:i.$store,mounted:function(){var e=this;this.$on("close",(function(t){e.handleClose()}))},methods:{handleClose:function(){var e=this;c(this.$refs._component.onCancel,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("cancel"),u.$destroy()}))},handleOk:function(){var e=this;c(this.$refs._component.onOK||this.$refs._component.onOk,(function(){e.visible=!1,e.$refs._component.$emit("close"),e.$refs._component.$emit("ok"),u.$destroy()}))}},render:function(e){var i=this,a=o&&o.model;a&&delete o.model;var c=Object.assign({},a&&{model:a}||{},{attrs:Object.assign({},(0,r.A)({},o.attrs||o),{visible:this.visible}),on:Object.assign({},(0,r.A)({},o.on||o),{ok:function(){i.handleOk()},cancel:function(){i.handleClose()}})}),u=n&&n.model;u&&delete n.model;var l=Object.assign({},u&&{model:u}||{},{ref:"_component",attrs:Object.assign({},(0,r.A)({},n&&n.attrs||n)),on:Object.assign({},(0,r.A)({},n&&n.on||n))});return e(s.A,c,[e(t,l)])}}).$mount(a)}}Object.defineProperty(e.prototype,"$dialog",{get:function(){return function(){t.apply(this,arguments)}}})}},8815:function(e,t,n){"use strict";n.d(t,{A:function(){return p}});var r,s,o=n(85471),i=new o.Ay,a=n(76338),c=(n(2008),n(50113),n(74423),n(62062),n(62010),n(26099),n(21699),n(23500),{name:"MultiTab",data:function(){return{fullPathList:[],pages:[],activeKey:"",newTabIndex:0}},created:function(){var e=this;i.$on("open",(function(t){if(!t)throw new Error("multi-tab: open tab ".concat(t," err"));e.activeKey=t})).$on("close",(function(t){t?e.closeThat(t):e.closeThat(e.activeKey)})).$on("rename",(function(t){var n=t.key,r=t.name;console.log("rename",n,r);try{var s=e.pages.find((function(e){return e.path===n}));s.meta.customTitle=r,e.$forceUpdate()}catch(o){}})),this.pages.push(this.$route),this.fullPathList.push(this.$route.fullPath),this.selectedLastPath()},methods:{onEdit:function(e,t){this[t](e)},remove:function(e){this.pages=this.pages.filter((function(t){return t.fullPath!==e})),this.fullPathList=this.fullPathList.filter((function(t){return t!==e})),this.fullPathList.includes(this.activeKey)||this.selectedLastPath()},selectedLastPath:function(){this.activeKey=this.fullPathList[this.fullPathList.length-1]},closeThat:function(e){this.fullPathList.length>1?this.remove(e):this.$message.info("这是最后一个标签了, 无法被关闭")},closeLeft:function(e){var t=this,n=this.fullPathList.indexOf(e);n>0?this.fullPathList.forEach((function(e,r){r<n&&t.remove(e)})):this.$message.info("左侧没有标签")},closeRight:function(e){var t=this,n=this.fullPathList.indexOf(e);n<this.fullPathList.length-1?this.fullPathList.forEach((function(e,r){r>n&&t.remove(e)})):this.$message.info("右侧没有标签")},closeAll:function(e){var t=this,n=this.fullPathList.indexOf(e);this.fullPathList.forEach((function(e,r){r!==n&&t.remove(e)}))},closeMenuClick:function(e,t){this[e](t)},renderTabPaneMenu:function(e){var t=this,n=this.$createElement;return n("a-menu",{on:(0,a.A)({},{click:function(n){var r=n.key;n.item,n.domEvent;t.closeMenuClick(r,e)}})},[n("a-menu-item",{key:"closeThat"},["关闭当前标签"]),n("a-menu-item",{key:"closeRight"},["关闭右侧"]),n("a-menu-item",{key:"closeLeft"},["关闭左侧"]),n("a-menu-item",{key:"closeAll"},["关闭全部"])])},renderTabPane:function(e,t){var n=this.$createElement,r=this.renderTabPaneMenu(t);return n("a-dropdown",{attrs:{overlay:r,trigger:["contextmenu"]}},[n("span",{style:{userSelect:"none"}},[e])])}},watch:{$route:function(e){this.activeKey=e.fullPath,this.fullPathList.indexOf(e.fullPath)<0&&(this.fullPathList.push(e.fullPath),this.pages.push(e))},activeKey:function(e){this.$router.push({path:e})}},render:function(){var e=this,t=arguments[0],n=this.onEdit,r=this.$data.pages,s=r.map((function(n){return t("a-tab-pane",{style:{height:0},attrs:{tab:e.renderTabPane(n.meta.customTitle||n.meta.title,n.fullPath),closable:r.length>1},key:n.fullPath})}));return t("div",{class:"ant-pro-multi-tab"},[t("div",{class:"ant-pro-multi-tab-wrapper"},[t("a-tabs",{attrs:{hideAdd:!0,type:"editable-card",tabBarStyle:{background:"#FFF",margin:0,paddingLeft:"16px",paddingTop:"1px"}},on:(0,a.A)({},{edit:n}),model:{value:e.activeKey,callback:function(t){e.activeKey=t}}},[s])])])}}),u=c,l=n(81656),d=(0,l.A)(u,r,s,!1,null,null,null),f=d.exports,m={open:function(e){i.$emit("open",e)},rename:function(e,t){i.$emit("rename",{key:e,name:t})},closeCurrentPage:function(){this.close()},close:function(e){i.$emit("close",e)}};f.install=function(e){e.prototype.$multiTab||(m.instance=i,e.prototype.$multiTab=m,e.component("multi-tab",f))};var p=f},60366:function(e,t){"use strict";t.A={navTheme:"dark",primaryColor:"#1890ff",layout:"topmenu",contentWidth:"Fixed",fixedHeader:!1,fixSiderbar:!1,colorWeak:!1,menu:{locale:!0},title:"遥遥领先",pwa:!1,iconfontUrl:"",production:!1}},34962:function(e,t,n){"use strict";n.d(t,{y:function(){return _e},f:function(){return $e}});n(26099),n(47764),n(62953);var r,s,o,i,a=function(){var e=this,t=e._self._c;return t("div",{class:["user-layout-wrapper",e.isMobile&&"mobile"],attrs:{id:"userLayout"}},[t("div",{staticClass:"container"},[t("div",{staticClass:"user-layout-lang"},[t("select-lang",{staticClass:"select-lang-trigger"})],1),t("div",{staticClass:"user-layout-content"},[t("router-view")],1),t("div",{attrs:{id:"particles-js"}})])])},c=[],u=n(99547),l=(n(96205),n(77197)),d=(n(50769),n(40255)),f=(n(17735),n(36457)),m=(n(62062),n(11363)),p=n(76338),h=n(95353),g={computed:(0,p.A)({},(0,h.aH)({currentLang:function(e){return e.app.lang}})),methods:{setLang:function(e){this.$store.dispatch("setLang",e)}}},b=g,y=["zh-CN","en-US"],v={"zh-CN":"简体中文","en-US":"English"},A={"zh-CN":"🇨🇳","en-US":"🇺🇸"},j={props:{prefixCls:{type:String,default:"ant-pro-drop-down"}},name:"SelectLang",mixins:[b],render:function(){var e=this,t=arguments[0],n=this.prefixCls,r=function(t){var n=t.key;e.setLang(n)},s=t(f.Ay,{class:["menu","ant-pro-header-menu"],attrs:{selectedKeys:[this.currentLang]},on:{click:r}},[y.map((function(e){return t(f.Ay.Item,{key:e},[t("span",{attrs:{role:"img","aria-label":v[e]}},[A[e]])," ",v[e]])}))]);return t(l.Ay,{attrs:{overlay:s,placement:"bottomRight"}},[t("span",{class:n},[t(d.A,{attrs:{type:"global",title:(0,m.vb)("navBar.lang")}})])])}},k=j,w={name:"UserLayout",components:{SelectLang:k},mixins:[u.w],mounted:function(){document.body.classList.add("userLayout"),this._animateBg()},beforeDestroy:function(){document.body.classList.remove("userLayout")},methods:{_animateBg:function(){particlesJS("particles-js",{particles:{number:{value:6,density:{enable:!0,value_area:800}},color:{value:"#1b1e34"},shape:{type:"polygon",stroke:{width:0,color:"#000"},polygon:{nb_sides:6},image:{src:"img/github.svg",width:100,height:100}},opacity:{value:.3,random:!0,anim:{enable:!1,speed:1,opacity_min:.1,sync:!1}},size:{value:160,random:!1,anim:{enable:!0,speed:10,size_min:40,sync:!1}},line_linked:{enable:!1,distance:200,color:"#ffffff",opacity:1,width:2},move:{enable:!0,speed:8,direction:"none",random:!1,straight:!1,out_mode:"out",bounce:!1,attract:{enable:!1,rotateX:600,rotateY:1200}}},interactivity:{detect_on:"canvas",events:{onhover:{enable:!1,mode:"grab"},onclick:{enable:!1,mode:"push"},resize:!0},modes:{grab:{distance:400,line_linked:{opacity:1}},bubble:{distance:400,size:40,duration:2,opacity:8,speed:3},repulse:{distance:200,duration:.4},push:{particles_nb:4},remove:{particles_nb:2}}},retina_detect:!0},(function(){console.log("callback - particles.js config loaded")}))}}},S=w,C=n(81656),x=(0,C.A)(S,a,c,!1,null,"446c7ccf",null),_=x.exports,$=function(){var e=this,t=e._self._c;return t("div",[t("router-view")],1)},T=[],M={name:"BlankLayout"},P=M,z=(0,C.A)(P,$,T,!1,null,"7f25f9eb",null),L=(z.exports,function(){var e=this,t=e._self._c;return t("pro-layout",e._b({attrs:{menus:e.visibleMenuData,collapsed:e.collapsed,mediaQuery:e.query,isMobile:e.isMobile,handleMediaQuery:e.handleMediaQuery,handleCollapse:e.handleCollapse,i18nRender:e.i18nRender},scopedSlots:e._u([{key:"menuHeaderRender",fn:function(){return[t("div",[t("img",{attrs:{src:n(33153)}}),t("h1",[e._v(e._s(e.title))])])]},proxy:!0},{key:"rightContentRender",fn:function(){return[t("right-content",{attrs:{"top-menu":"topmenu"===e.settings.layout,"is-mobile":e.isMobile,theme:e.settings.theme}})]},proxy:!0},{key:"footerRender",fn:function(){return[t("global-footer")]},proxy:!0}])},"pro-layout",e.settings,!1),[e.isDev?t("setting-drawer",{attrs:{settings:e.settings},on:{change:e.handleSettingChange}},[t("div",{staticStyle:{margin:"12px 0"}},[e._v("This is SettingDrawer custom footer content.")])]):e._e(),t("router-view")],1)}),E=[],U=(n(2008),n(50113),n(20931)),F=n(75314),N=n(60366),O=function(){var e=this,t=e._self._c;return t("div",{class:e.wrpCls},[t("avatar-dropdown",{class:e.prefixCls,attrs:{menu:e.showMenu,"current-user":e.currentUser}}),t("select-lang",{class:e.prefixCls})],1)},D=[],H=n(26297),R=(n(28706),n(34782),n(62010),function(){var e=this,t=e._self._c;return t("div",[e.currentUser&&e.currentUser.name?t("a-dropdown",{attrs:{placement:"bottomRight"},scopedSlots:e._u([{key:"overlay",fn:function(){return[t("a-menu",{staticClass:"ant-pro-drop-down menu",attrs:{"selected-keys":[]}},[e.menu?t("a-menu-item",{key:"center",on:{click:e.handleToCenter}},[t("a-icon",{attrs:{type:"user"}}),e._v(" "+e._s(e.$t("menu.account.center"))+" ")],1):e._e(),e.menu?t("a-menu-item",{key:"settings",on:{click:e.handleToSettings}},[t("a-icon",{attrs:{type:"setting"}}),e._v(" "+e._s(e.$t("menu.account.settings"))+" ")],1):e._e(),e.menu?t("a-menu-divider"):e._e(),e.menu?t("a-menu-item",{key:"acct"},[t("a-icon",{attrs:{type:"bulb"}}),e._v(" 账号状态 "+e._s(e.PbAcctState)+" ")],1):e._e(),e.menu?t("a-menu-item",{key:"send"},[t("a-icon",{attrs:{type:"branches"}}),e._v(" 请求状态 "+e._s(e.PbSendState)+" ")],1):e._e(),e.menu?t("a-menu-divider"):e._e(),"1"===e.currentUser.vip?t("a-menu-item",{key:"hg",on:{click:function(t){return e.openUrl()}}},[t("a-icon",{attrs:{type:"branches"}}),e._v(" 打开下单窗口 ")],1):e._e(),"1"===e.currentUser.vip?t("a-menu-item",{key:"hg1",on:{click:function(t){return e.openUrlEx()}}},[t("a-icon",{attrs:{type:"branches"}}),e._v(" 打开补单窗口 ")],1):e._e(),e.menu?t("a-menu-divider"):e._e(),t("a-menu-item",{key:"logout",on:{click:e.handleLogout}},[t("a-icon",{attrs:{type:"logout"}}),e._v(" "+e._s(e.$t("menu.account.logout"))+" ")],1)],1)]},proxy:!0}],null,!1,********)},[t("span",{staticClass:"ant-pro-account-avatar"},[t("a-avatar",{staticClass:"antd-pro-global-header-index-avatar",attrs:{size:"small",src:"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"}}),t("span",{staticStyle:{color:"#FFF"}},[e._v(e._s(e.currentUser.name)+" "+e._s(e.currentUser.viptime))])],1)]):t("span",[t("a-spin",{style:{marginLeft:8,marginRight:8},attrs:{size:"small"}})],1),t("a-modal",{attrs:{title:"下单窗口信息"},on:{ok:e.handleOk},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[t("a-form",{attrs:{layout:"inline",form:e.form}},[t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["url",{rules:[{required:!0,message:"请填写hg网址!"}],initialValue:"https://hga035.com"}],expression:"[\n            'url',\n            { rules: [{ required: true, message: '请填写hg网址!' }],initialValue: 'https://hga035.com' },\n          ]"}],attrs:{placeholder:"请填写hg网址"}},[t("a-icon",{staticStyle:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"user"},slot:"prefix"})],1)],1),"bd"===e.type?t("a-form-item",[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["sendData",{rules:[{message:"填写本地vpn端口!"}],initialValue:"10808"}],expression:"[\n            'sendData',\n            { rules: [{ message: '填写本地vpn端口!' }],initialValue: '10808' },\n          ]"}],attrs:{placeholder:"填写本地vpn端口"}},[t("a-icon",{staticStyle:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1):e._e()],1)],1)],1)}),q=[],I=(n(96305),n(43898)),V=n(41036),W=n(505),B=n(44847),Y=window,G=Y.ipcRendererChannel,K={name:"AvatarDropdown",props:{currentUser:{type:Object,default:function(){return null}},menu:{type:Boolean,default:!0}},data:function(){return{PbAcctState:"",PbSendState:"",PbSendMsg:"",errortip:!1,form:this.$form.createForm(this,{url:"https://hga035.com",sendData:"10808"}),visible:!1,type:"",timer:null}},mounted:function(){var e=this;setTimeout((function(){G.PbAcctState.on((function(t,n){e.PbAcctState=n.state,"noacct"===n.state&&!1===e.errortip&&(e.errortip=!0)})),G.PbSendState.on((function(t,n){e.PbSendState=n.state,e.PbSendMsg=n.msg,"error"===n.state&&!1===e.errortip?e.errortip=!0:"ok"===n.state&&!0===e.errortip&&(e.$notification.destroy(),e.errortip=!1)}))}),3e3),this.timer=(0,B.setInterval)((function(){e.currentUser.uuid&&(0,V.$c)({uuid:e.currentUser.uuid,photosrc:e.PbSendState+"|"+(e.PbSendMsg||"00"),email:e.PbAcctState}),"noacct"!==e.PbAcctState&&"error"!==e.PbSendState||e.$store.dispatch("GetInfo")}),3e4)},beforeDestroy:function(){G.PbAcctState.removeAllListeners(),G.PbSendState.removeAllListeners(),clearInterval(this.timer)},methods:{handleToCenter:function(){this.$router.push({path:"/account/center"})},handleToSettings:function(){this.$router.push({path:"/account/settings"})},handleLogout:function(e){var t=this;I.A.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),onOk:function(){return t.$store.dispatch("Logout").then((function(){t.$router.push({name:"login"})}))},onCancel:function(){}})},openUrl:function(){this.type="",this.visible=!0},openUrlEx:function(){this.type="bd",this.visible=!0},handleOk:function(e){var t=this;this.form.validateFields((function(e,n){if(!e){t.visible=!1;try{"bd"===t.type?(0,W.Av)({url:n.url,sendData:n.sendData}):(0,W.ZA)({url:n.url})}catch(r){t.$message.error("请更新客户端至3.0.3版本")}}}))}}},Q=K,X=(0,C.A)(Q,R,q,!1,null,"69bd7904",null),Z=X.exports,J={name:"RightContent",components:{AvatarDropdown:Z,SelectLang:k},props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:function(){return!1}},topMenu:{type:Boolean,required:!0},theme:{type:String,required:!0}},data:function(){return(0,p.A)((0,p.A)({showMenu:!0},(0,h.L8)(["account","avatar","userInfo"])),{},{currentUser:{}})},computed:{wrpCls:function(){return(0,H.A)({"ant-pro-global-header-index-right":!0},"ant-pro-global-header-index-".concat(this.isMobile||!this.topMenu?"light":this.theme),!0)}},methods:{timestampToDateTime:function(e){var t=new Date(e),n=t.getFullYear(),r=("0"+(t.getMonth()+1)).slice(-2),s=("0"+t.getDate()).slice(-2),o=("0"+t.getHours()).slice(-2),i=("0"+t.getMinutes()).slice(-2),a=("0"+t.getSeconds()).slice(-2);return"".concat(n,"-").concat(r,"-").concat(s," ").concat(o,":").concat(i,":").concat(a)}},mounted:function(){var e=this;setTimeout((function(){e.currentUser={name:e.account(),viptime:e.timestampToDateTime(e.userInfo().viptime),uuid:e.userInfo().uuid,role:e.userInfo().role,vip:e.userInfo().vip}}),1500)}},ee=J,te=(0,C.A)(ee,O,D,!1,null,null,null),ne=te.exports,re=function(){var e=this,t=e._self._c;return t("global-footer",{staticClass:"footer custom-render"})},se=[],oe={name:"ProGlobalFooter",components:{GlobalFooter:U.Tn}},ie=oe,ae=(0,C.A)(ie,re,se,!1,null,null,null),ce=ae.exports,ue="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",le={props:{isMobile:Boolean},mounted:function(){},methods:{load:function(){if(ue){var e=document.createElement("script");e.id="_adsbygoogle_js",e.src=ue,this.$el.appendChild(e),setTimeout((function(){(window.adsbygoogle||[]).push({})}),2e3)}}},render:function(){var e=arguments[0];return e("div",{class:"business-pro-ad"},[e("a",{attrs:{href:"https://store.antdv.com/pro/",target:"_blank"}},["(推荐) 企业级商用版 Admin Pro 现已发售，采用 Vue3 + TS 欢迎购买。"])])}},de=le,fe=(0,C.A)(de,r,s,!1,null,"4109f67d",null),me=fe.exports,pe={name:"BasicLayout",components:{SettingDrawer:U.G5,RightContent:ne,GlobalFooter:ce,Ads:me},data:function(){return{isProPreviewSite:!0,isDev:!0,menus:[],collapsed:!1,title:N.A.title,settings:{layout:N.A.layout,contentWidth:"sidemenu"===N.A.layout?F.OT.Fluid:N.A.contentWidth,theme:N.A.navTheme,primaryColor:N.A.primaryColor,fixedHeader:N.A.fixedHeader,fixSiderbar:N.A.fixSiderbar,colorWeak:N.A.colorWeak,hideHintAlert:!1,hideCopyButton:!1},query:{},isMobile:!1}},computed:(0,p.A)((0,p.A)({},(0,h.aH)({mainMenu:function(e){return e.permission.addRouters}})),{},{visibleMenuData:function(){var e=this;return this.menus.filter((function(e){return!e.meta.hideInMenu})).map((function(t){return t.children&&(t.children=e.filterChildren(t.children)),t}))}}),created:function(){var e=this,t=this.mainMenu.find((function(e){return"/"===e.path}));this.menus=t&&t.children||[],this.$watch("collapsed",(function(){e.$store.commit(F.cf,e.collapsed)})),this.$watch("isMobile",(function(){e.$store.commit(F.nd,e.isMobile)}))},mounted:function(){var e=this,t=navigator.userAgent;t.indexOf("Edge")>-1&&this.$nextTick((function(){e.collapsed=!e.collapsed,setTimeout((function(){e.collapsed=!e.collapsed}),16)})),(0,U.V_)(this.settings.primaryColor)},methods:{i18nRender:m.vb,handleMediaQuery:function(e){this.query=e,!this.isMobile||e["screen-xs"]?!this.isMobile&&e["screen-xs"]&&(this.isMobile=!0,this.collapsed=!1,this.settings.contentWidth=F.OT.Fluid):this.isMobile=!1},handleCollapse:function(e){this.collapsed=e},handleSettingChange:function(e){var t=e.type,n=e.value;switch(console.log("type",t,n),t&&(this.settings[t]=n),t){case"contentWidth":this.settings[t]=n;break;case"layout":"sidemenu"===n?this.settings.contentWidth=F.OT.Fluid:(this.settings.fixSiderbar=!1,this.settings.contentWidth=F.OT.Fixed);break}},filterChildren:function(e){var t=this;return e.filter((function(e){return!e.meta||!e.meta.hideInMenu})).map((function(e){return e.children&&(e.children=t.filterChildren(e.children)),e}))}}},he=pe,ge=(0,C.A)(he,L,E,!1,null,null,null),be=ge.exports,ye={name:"RouteView",props:{keepAlive:{type:Boolean,default:!0}},data:function(){return{}},render:function(){var e=arguments[0],t=this.$route.meta,n=this.$store.getters,r=e("keep-alive",[e("router-view")]),s=e("router-view");return(n.multiTab||t.keepAlive)&&(this.keepAlive||n.multiTab||t.keepAlive)?r:s}},ve=ye,Ae=(0,C.A)(ve,o,i,!1,null,null,null),je=(Ae.exports,function(){var e=this,t=e._self._c;return t("page-header-wrapper",[t("router-view")],1)}),ke=[],we={name:"PageView"},Se=we,Ce=(0,C.A)(Se,je,ke,!1,null,null,null),xe=(Ce.exports,n(98490),{name:"RouteView",render:function(e){return e("router-view")}}),_e=[{path:"/",name:"index",component:be,meta:{title:"遥遥领先"},redirect:"/football",children:[{path:"/football",redirect:"/football/today",component:xe,meta:{title:"足球预测",icon:"line-chart",permission:["superadmin","admin","user"]},children:[{path:"/football/today",name:"TodayMatch",component:function(){return Promise.all([n.e(39),n.e(437)]).then(n.bind(n,17437))},meta:{title:"今日赛事",keepAlive:!0,permission:["superadmin","admin","user"]}}]},{path:"/sysconfig",redirect:"/sysconfig/users",component:xe,meta:{title:"系统配置",icon:"form",permission:["admin"]},children:[{path:"/sysconfig/users",name:"usersets",component:function(){return Promise.all([n.e(39),n.e(915)]).then(n.bind(n,11915))},meta:{title:"用户设置",keepAlive:!0,permission:["admin"]}},{path:"/sysconfig/uidset",name:"uidset",component:function(){return Promise.all([n.e(39),n.e(96)]).then(n.bind(n,70096))},meta:{title:"hguid配置",keepAlive:!0,permission:["admin"]}},{path:"/sysconfig/pbcookie",name:"pbcookie",component:function(){return Promise.all([n.e(39),n.e(982)]).then(n.bind(n,86982))},meta:{title:"pbcookie配置",keepAlive:!0,permission:["admin"]}},{path:"/sysconfig/teamconfig",name:"teamconfig",component:function(){return Promise.all([n.e(39),n.e(555)]).then(n.bind(n,9555))},meta:{title:"队名配置",keepAlive:!0,permission:["admin"]}}]}]},{path:"*",redirect:"/404",hidden:!0}],$e=[{path:"/user",component:_,redirect:"/user/login",hidden:!0,children:[{path:"login",name:"login",component:function(){return n.e(806).then(n.bind(n,98852))}},{path:"register",name:"register",component:function(){return n.e(806).then(n.bind(n,71194))}},{path:"register-result",name:"registerResult",component:function(){return n.e(806).then(n.bind(n,2275))}},{path:"recover",name:"recover",component:void 0}]},{path:"/404",component:function(){return n.e(143).then(n.bind(n,32319))}}]},11363:function(e,t,n){"use strict";n.d(t,{J4:function(){return g},vb:function(){return b}});var r=n(76338),s=(n(74423),n(26099),n(47764),n(62953),n(85471)),o=n(64765),i=n(74053),a=n.n(i),c=n(95093),u=n.n(c),l=n(48188);s.Ay.use(o.A);var d="zh-CN",f={"zh-CN":(0,r.A)({},l["default"])},m=new o.A({silentTranslationWarn:!0,locale:d,fallbackLocale:d,messages:f}),p=[d];function h(e){return m.locale=e,document.querySelector("html").setAttribute("lang",e),e}function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d;return new Promise((function(t){return a().set("lang",e),m.locale!==e?p.includes(e)?t(h(e)):n(5839)("./".concat(e)).then((function(t){var n=t.default;return m.setLocaleMessage(e,n),p.push(e),u().updateLocale(n.momentName,n.momentLocale),h(e)})):t(e)}))}function b(e){return m.t("".concat(e))}t.Ay=m},48188:function(e,t,n){"use strict";n.r(t);var r=n(76338),s=n(95692),o=n(52648),i=n.n(o),a=n(39668),c=n(14868),u=n(91363),l=n(11016),d=n(91771),f=n(89065),m=n(11250),p=n(77844),h={antLocale:s.A,momentName:"zh-cn",momentLocale:i()};t["default"]=(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({message:"-","layouts.usermenu.dialog.title":"信息","layouts.usermenu.dialog.content":"您确定要注销吗？","layouts.userLayout.title":"公众号平台统一管理系统"},h),a["default"]),c["default"]),u["default"]),l["default"]),d["default"]),f["default"]),m["default"]),p["default"])},77844:function(e,t,n){"use strict";n.r(t);var r=n(76338),s=n(54142);t["default"]=(0,r.A)({},s["default"])},54142:function(e,t,n){"use strict";n.r(t),t["default"]={"account.settings.menuMap.basic":"基本设置","account.settings.menuMap.security":"安全设置","account.settings.menuMap.custom":"个性化","account.settings.menuMap.binding":"账号绑定","account.settings.menuMap.notification":"新消息通知","account.settings.basic.avatar":"头像","account.settings.basic.change-avatar":"更换头像","account.settings.basic.email":"邮箱","account.settings.basic.email-message":"请输入您的邮箱!","account.settings.basic.nickname":"昵称","account.settings.basic.nickname-message":"请输入您的昵称!","account.settings.basic.profile":"个人简介","account.settings.basic.profile-message":"请输入个人简介!","account.settings.basic.profile-placeholder":"个人简介","account.settings.basic.country":"国家/地区","account.settings.basic.country-message":"请输入您的国家或地区!","account.settings.basic.geographic":"所在省市","account.settings.basic.geographic-message":"请输入您的所在省市!","account.settings.basic.address":"街道地址","account.settings.basic.address-message":"请输入您的街道地址!","account.settings.basic.phone":"联系电话","account.settings.basic.phone-message":"请输入您的联系电话!","account.settings.basic.update":"更新基本信息","account.settings.basic.update.success":"更新基本信息成功","account.settings.security.strong":"强","account.settings.security.medium":"中","account.settings.security.weak":"弱","account.settings.security.password":"账户密码","account.settings.security.password-description":"当前密码强度：","account.settings.security.phone":"密保手机","account.settings.security.phone-description":"已绑定手机：","account.settings.security.question":"密保问题","account.settings.security.question-description":"未设置密保问题，密保问题可有效保护账户安全","account.settings.security.email":"备用邮箱","account.settings.security.email-description":"已绑定邮箱：","account.settings.security.mfa":"MFA 设备","account.settings.security.mfa-description":"未绑定 MFA 设备，绑定后，可以进行二次确认","account.settings.security.modify":"修改","account.settings.security.set":"设置","account.settings.security.bind":"绑定","account.settings.binding.taobao":"绑定淘宝","account.settings.binding.taobao-description":"当前未绑定淘宝账号","account.settings.binding.alipay":"绑定支付宝","account.settings.binding.alipay-description":"当前未绑定支付宝账号","account.settings.binding.dingding":"绑定钉钉","account.settings.binding.dingding-description":"当前未绑定钉钉账号","account.settings.binding.bind":"绑定","account.settings.notification.password":"账户密码","account.settings.notification.password-description":"其他用户的消息将以站内信的形式通知","account.settings.notification.messages":"系统消息","account.settings.notification.messages-description":"系统消息将以站内信的形式通知","account.settings.notification.todo":"待办任务","account.settings.notification.todo-description":"待办任务将以站内信的形式通知","account.settings.settings.open":"开","account.settings.settings.close":"关"}},91771:function(e,t,n){"use strict";n.r(t);var r=n(76338),s=n(22492);t["default"]=(0,r.A)({},s["default"])},22492:function(e,t,n){"use strict";n.r(t),t["default"]={"dashboard.analysis.test":"工专路 {no} 号店","dashboard.analysis.introduce":"指标说明","dashboard.analysis.total-sales":"总销售额","dashboard.analysis.day-sales":"日均销售额￥","dashboard.analysis.visits":"访问量","dashboard.analysis.visits-trend":"访问量趋势","dashboard.analysis.visits-ranking":"门店访问量排名","dashboard.analysis.day-visits":"日访问量","dashboard.analysis.week":"周同比","dashboard.analysis.day":"日同比","dashboard.analysis.payments":"支付笔数","dashboard.analysis.conversion-rate":"转化率","dashboard.analysis.operational-effect":"运营活动效果","dashboard.analysis.sales-trend":"销售趋势","dashboard.analysis.sales-ranking":"门店销售额排名","dashboard.analysis.all-year":"全年","dashboard.analysis.all-month":"本月","dashboard.analysis.all-week":"本周","dashboard.analysis.all-day":"今日","dashboard.analysis.search-users":"搜索用户数","dashboard.analysis.per-capita-search":"人均搜索次数","dashboard.analysis.online-top-search":"线上热门搜索","dashboard.analysis.the-proportion-of-sales":"销售额类别占比","dashboard.analysis.dropdown-option-one":"操作一","dashboard.analysis.dropdown-option-two":"操作二","dashboard.analysis.channel.all":"全部渠道","dashboard.analysis.channel.online":"线上","dashboard.analysis.channel.stores":"门店","dashboard.analysis.sales":"销售额","dashboard.analysis.traffic":"客流量","dashboard.analysis.table.rank":"排名","dashboard.analysis.table.search-keyword":"搜索关键词","dashboard.analysis.table.users":"用户数","dashboard.analysis.table.weekly-range":"周涨幅"}},89065:function(e,t,n){"use strict";n.r(t);var r=n(76338),s=n(97178);t["default"]=(0,r.A)({},s["default"])},97178:function(e,t,n){"use strict";n.r(t),t["default"]={"form.basic-form.basic.title":"基础表单","form.basic-form.basic.description":"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。","form.basic-form.title.label":"标题","form.basic-form.title.placeholder":"给目标起个名字","form.basic-form.title.required":"请输入标题","form.basic-form.date.label":"起止日期","form.basic-form.placeholder.start":"开始日期","form.basic-form.placeholder.end":"结束日期","form.basic-form.date.required":"请选择起止日期","form.basic-form.goal.label":"目标描述","form.basic-form.goal.placeholder":"请输入你的阶段性工作目标","form.basic-form.goal.required":"请输入目标描述","form.basic-form.standard.label":"衡量标准","form.basic-form.standard.placeholder":"请输入衡量标准","form.basic-form.standard.required":"请输入衡量标准","form.basic-form.client.label":"客户","form.basic-form.client.required":"请描述你服务的客户","form.basic-form.label.tooltip":"目标的服务对象","form.basic-form.client.placeholder":"请描述你服务的客户，内部客户直接 @姓名／工号","form.basic-form.invites.label":"邀评人","form.basic-form.invites.placeholder":"请直接 @姓名／工号，最多可邀请 5 人","form.basic-form.weight.label":"权重","form.basic-form.weight.placeholder":"请输入","form.basic-form.public.label":"目标公开","form.basic-form.label.help":"客户、邀评人默认被分享","form.basic-form.radio.public":"公开","form.basic-form.radio.partially-public":"部分公开","form.basic-form.radio.private":"不公开","form.basic-form.publicUsers.placeholder":"公开给","form.basic-form.option.A":"同事一","form.basic-form.option.B":"同事二","form.basic-form.option.C":"同事三","form.basic-form.email.required":"请输入邮箱地址！","form.basic-form.email.wrong-format":"邮箱地址格式错误！","form.basic-form.userName.required":"请输入用户名!","form.basic-form.password.required":"请输入密码！","form.basic-form.password.twice":"两次输入的密码不匹配!","form.basic-form.strength.msg":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","form.basic-form.strength.strong":"强度：强","form.basic-form.strength.medium":"强度：中","form.basic-form.strength.short":"强度：太短","form.basic-form.confirm-password.required":"请确认密码！","form.basic-form.phone-number.required":"请输入手机号！","form.basic-form.phone-number.wrong-format":"手机号格式错误！","form.basic-form.verification-code.required":"请输入验证码！","form.basic-form.form.get-captcha":"获取验证码","form.basic-form.captcha.second":"秒","form.basic-form.form.optional":"（选填）","form.basic-form.form.submit":"提交","form.basic-form.form.save":"保存","form.basic-form.email.placeholder":"邮箱","form.basic-form.password.placeholder":"至少6位密码，区分大小写","form.basic-form.confirm-password.placeholder":"确认密码","form.basic-form.phone-number.placeholder":"手机号","form.basic-form.verification-code.placeholder":"验证码"}},39668:function(e,t,n){"use strict";n.r(t),t["default"]={submit:"提交",save:"保存","submit.ok":"提交成功","save.ok":"保存成功"}},14868:function(e,t,n){"use strict";n.r(t),t["default"]={"menu.welcome":"欢迎","menu.home":"主页","menu.dashboard":"平台概况","menu.dashboard.analysis":"数据分析","menu.dashboard.workplace":"活动动态","menu.form":"表单页","menu.form.basic-form":"基础表单","menu.form.step-form":"分步表单","menu.form.step-form.info":"分步表单（填写转账信息）","menu.form.step-form.confirm":"分步表单（确认转账信息）","menu.form.step-form.result":"分步表单（完成）","menu.form.advanced-form":"高级表单","menu.list":"列表页","menu.list.table-list":"查询表格","menu.list.basic-list":"标准列表","menu.list.card-list":"卡片列表","menu.list.search-list":"搜索列表","menu.list.search-list.articles":"搜索列表（文章）","menu.list.search-list.projects":"搜索列表（项目）","menu.list.search-list.applications":"搜索列表（应用）","menu.profile":"详情页","menu.profile.basic":"基础详情页","menu.profile.advanced":"高级详情页","menu.result":"结果页","menu.result.success":"成功页","menu.result.fail":"失败页","menu.exception":"异常页","menu.exception.not-permission":"403","menu.exception.not-find":"404","menu.exception.server-error":"500","menu.exception.trigger":"触发错误","menu.account":"个人页","menu.account.center":"个人中心","menu.account.settings":"个人设置","menu.account.trigger":"触发报错","menu.account.logout":"退出登录"}},11250:function(e,t,n){"use strict";n.r(t);var r=n(76338),s=n(64158),o=n(27669);t["default"]=(0,r.A)((0,r.A)({},s["default"]),o["default"])},27669:function(e,t,n){"use strict";n.r(t),t["default"]={"result.fail.error.title":"提交失败","result.fail.error.description":"请核对并修改以下信息后，再重新提交。","result.fail.error.hint-title":"您提交的内容有如下错误：","result.fail.error.hint-text1":"您的账户已被冻结","result.fail.error.hint-btn1":"立即解冻","result.fail.error.hint-text2":"您的账户还不具备申请资格","result.fail.error.hint-btn2":"立即升级","result.fail.error.btn-text":"返回修改"}},64158:function(e,t,n){"use strict";n.r(t),t["default"]={"result.success.title":"提交成功","result.success.description":"提交结果页用于反馈一系列操作任务的处理结果， 如果仅是简单操作，使用 Message 全局提示反馈即可。 本文字区域可以展示简单的补充说明，如果有类似展示 “单据”的需求，下面这个灰色区域可以呈现比较复杂的内容。","result.success.operate-title":"项目名称","result.success.operate-id":"项目 ID","result.success.principal":"负责人","result.success.operate-time":"生效时间","result.success.step1-title":"创建项目","result.success.step1-operator":"曲丽丽","result.success.step2-title":"部门初审","result.success.step2-operator":"周毛毛","result.success.step2-extra":"催一下","result.success.step3-title":"财务复核","result.success.step4-title":"完成","result.success.btn-return":"返回列表","result.success.btn-project":"查看项目","result.success.btn-print":"打印"}},91363:function(e,t,n){"use strict";n.r(t),t["default"]={"app.setting.pagestyle":"整体风格设置","app.setting.pagestyle.light":"亮色菜单风格","app.setting.pagestyle.dark":"暗色菜单风格","app.setting.pagestyle.realdark":"暗黑模式","app.setting.themecolor":"主题色","app.setting.navigationmode":"导航模式","app.setting.content-width":"内容区域宽度","app.setting.fixedheader":"固定 Header","app.setting.fixedsidebar":"固定侧边栏","app.setting.sidemenu":"侧边菜单布局","app.setting.topmenu":"顶部菜单布局","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.othersettings":"其他设置","app.setting.weakmode":"色弱模式","app.setting.copy":"拷贝设置","app.setting.loading":"加载主题中","app.setting.copyinfo":"拷贝设置成功 src/config/defaultSettings.js","app.setting.production.hint":"配置栏只在开发环境用于预览，生产环境不会展现，请拷贝后手动修改配置文件","app.setting.themecolor.daybreak":"拂晓蓝","app.setting.themecolor.dust":"薄暮","app.setting.themecolor.volcano":"火山","app.setting.themecolor.sunset":"日暮","app.setting.themecolor.cyan":"明青","app.setting.themecolor.green":"极光绿","app.setting.themecolor.geekblue":"极客蓝","app.setting.themecolor.purple":"酱紫"}},11016:function(e,t,n){"use strict";n.r(t),t["default"]={"user.login.userName":"用户名","user.login.password":"密码","user.login.username.placeholder":"账户: admin","user.login.password.placeholder":"密码: admin or ant.design","user.login.message-invalid-credentials":"账户或密码错误（admin/ant.design）","user.login.message-invalid-verification-code":"验证码错误","user.login.tab-login-credentials":"账户密码登录","user.login.tab-login-mobile":"手机号登录","user.login.mobile.placeholder":"手机号","user.login.mobile.verification-code.placeholder":"验证码","user.login.remember-me":"记住密码","user.login.forgot-password":"忘记密码","user.login.sign-in-with":"其他登录方式","user.login.signup":"注册账户","user.login.login":"登录","user.register.register":"注册","user.register.email.placeholder":"邮箱","user.register.password.placeholder":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.password.popover-message":"请至少输入 6 个字符。请不要使用容易被猜到的密码。","user.register.confirm-password.placeholder":"确认密码","user.register.get-verification-code":"获取验证码","user.register.sign-in":"使用已有账户登录","user.register-result.msg":"你的账户：{email} 注册成功","user.register-result.activation-email":"激活邮件已发送到你的邮箱中，邮件有效期为24小时。请及时登录邮箱，点击邮件中的链接激活帐户。","user.register-result.back-home":"返回首页","user.register-result.view-mailbox":"查看邮箱","user.email.required":"请输入邮箱地址！","user.email.wrong-format":"邮箱地址格式错误！","user.userName.required":"请输入帐户名或邮箱地址","user.password.required":"请输入密码！","user.password.twice.msg":"两次输入的密码不匹配!","user.password.strength.msg":"密码强度不够 ","user.password.strength.strong":"强度：强","user.password.strength.medium":"强度：中","user.password.strength.low":"强度：低","user.password.strength.short":"强度：太短","user.confirm-password.required":"请确认密码！","user.phone-number.required":"请输入正确的手机号","user.phone-number.wrong-format":"手机号格式错误！","user.verification-code.required":"请输入验证码！"}},45421:function(e,t,n){"use strict";n(23792),n(3362),n(69085),n(9391),n(52675),n(89463),n(66412),n(60193),n(92168),n(2259),n(86964),n(83237),n(61833),n(67947),n(31073),n(45700),n(78125),n(20326),n(28706),n(26835),n(33771),n(2008),n(50113),n(48980),n(46449),n(78350),n(23418),n(74423),n(48598),n(62062),n(31051),n(34782),n(26910),n(87478),n(54554),n(93514),n(30237),n(54743),n(46761),n(11745),n(89572),n(48957),n(62010),n(4731),n(36033),n(93153),n(82326),n(36389),n(64444),n(8085),n(77762),n(65070),n(60605),n(39469),n(72152),n(75376),n(56624),n(11367),n(5914),n(78553),n(98690),n(60479),n(70761),n(2892),n(45374),n(25428),n(32637),n(40150),n(59149),n(64601),n(44435),n(87220),n(25843),n(9868),n(17427),n(87607),n(5506),n(52811),n(53921),n(83851),n(81278),n(1480),n(40875),n(29908),n(94052),n(94003),n(221),n(79432),n(9220),n(7904),n(93967),n(93941),n(10287),n(26099),n(16034),n(39796),n(60825),n(87411),n(21211),n(40888),n(9065),n(86565),n(32812),n(84634),n(71137),n(30985),n(34268),n(34873),n(84864),n(27495),n(69479),n(38781),n(31415),n(23860),n(99449),n(27337),n(21699),n(47764),n(71761),n(35701),n(68156),n(85906),n(42781),n(25440),n(5746),n(90744),n(11392),n(42762),n(39202),n(43359),n(89907),n(11898),n(35490),n(5745),n(94298),n(60268),n(69546),n(20781),n(50778),n(89195),n(46276),n(48718),n(16308),n(34594),n(29833),n(46594),n(72107),n(95477),n(21489),n(22134),n(3690),n(61740),n(81630),n(72170),n(75044),n(69539),n(31694),n(89955),n(33206),n(48345),n(44496),n(66651),n(12887),n(19369),n(66812),n(8995),n(52568),n(31575),n(36072),n(88747),n(28845),n(29423),n(57301),n(373),n(86614),n(41405),n(33684),n(73772),n(30958),n(23500),n(62953),n(59848),n(122),n(3296),n(27208),n(48408),n(7452);var r=n(85471),s=function(){var e=this,t=e._self._c;return t("a-config-provider",{attrs:{locale:e.locale}},[t("div",{attrs:{id:"app"}},[t("router-view")],1)])},o=[],i=n(60366),a=function(e){document.title=e;var t=navigator.userAgent,n=/\bMicroMessenger\/([\d\.]+)/;if(n.test(t)&&/ip(hone|od|ad)/i.test(t)){var r=document.createElement("iframe");r.src="/favicon.ico",r.style.display="none",r.onload=function(){setTimeout((function(){r.remove()}),9)},document.body.appendChild(r)}},c=i.A.title,u=n(11363),l={data:function(){return{}},computed:{locale:function(){var e=this.$route.meta.title;return e&&a("".concat((0,u.vb)(e)," - ").concat(c)),this.$i18n.getLocaleMessage(this.$store.getters.lang).antLocale}}},d=l,f=n(81656),m=(0,f.A)(d,s,o,!1,null,null,null),p=m.exports,h=n(81625),g=n(55499),b=n(75769),y=n(20931),v={theme:[{key:"dark",fileName:"dark.css",theme:"dark"},{key:"#F5222D",fileName:"#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",fileName:"#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",fileName:"#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",fileName:"#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",fileName:"#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",fileName:"#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",fileName:"#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}},{key:"#F5222D",theme:"dark",fileName:"dark-#F5222D.css",modifyVars:{"@primary-color":"#F5222D"}},{key:"#FA541C",theme:"dark",fileName:"dark-#FA541C.css",modifyVars:{"@primary-color":"#FA541C"}},{key:"#FAAD14",theme:"dark",fileName:"dark-#FAAD14.css",modifyVars:{"@primary-color":"#FAAD14"}},{key:"#13C2C2",theme:"dark",fileName:"dark-#13C2C2.css",modifyVars:{"@primary-color":"#13C2C2"}},{key:"#52C41A",theme:"dark",fileName:"dark-#52C41A.css",modifyVars:{"@primary-color":"#52C41A"}},{key:"#2F54EB",theme:"dark",fileName:"dark-#2F54EB.css",modifyVars:{"@primary-color":"#2F54EB"}},{key:"#722ED1",theme:"dark",fileName:"dark-#722ED1.css",modifyVars:{"@primary-color":"#722ED1"}}]},A=(n(7427),n(74053)),j=n.n(A),k=n(75314),w=function(){console.log("[antd pro] created()");var e="\n █████╗ ███╗   ██╗████████╗██████╗     ██████╗ ██████╗  ██████╗ \n██╔══██╗████╗  ██║╚══██╔══╝██╔══██╗    ██╔══██╗██╔══██╗██╔═══██╗\n███████║██╔██╗ ██║   ██║   ██║  ██║    ██████╔╝██████╔╝██║   ██║\n██╔══██║██║╚██╗██║   ██║   ██║  ██║    ██╔═══╝ ██╔══██╗██║   ██║\n██║  ██║██║ ╚████║   ██║   ██████╔╝    ██║     ██║  ██║╚██████╔╝\n╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═════╝     ╚═╝     ╚═╝  ╚═╝ ╚═════╝ \n\t\t\t\t\tPublished ".concat("3.0.4","-").concat("9cb4e0c"," @ antdv.com\n\t\t\t\t\tBuild date: ").concat("2025/9/1 17:40:10");console.log("%c".concat(e),"color: #fc4d50"),console.log("%c感谢使用 antd pro!","color: #000; font-size: 14px;    font-family: Hiragino Sans GB,Microsoft YaHei,\\\\5FAE\\8F6F\\96C5\\9ED1,Droid Sans Fallback,Source Sans,Wenquanyi Micro Hei,WenQuanYi Micro Hei Mono,WenQuanYi Zen Hei,Apple LiGothic Medium,SimHei,ST Heiti,WenQuanYi Zen Hei Sharp,sans-serif;"),console.log("%cThanks for using antd pro!","color: #fff; font-size: 14px; font-weight: 300; text-shadow:#000 1px 0 0,#000 0 1px 0,#000 -1px 0 0,#000 0 -1px 0;"),console.log(""),console.log("%c默认使用的路由初始化模式可能是 静态路由 / 动态路由, 请前往 src/store/index.js 确认 import permission from 哪一个文件.","color: #000; font-size: 14px;    font-family: Hiragino Sans GB,Microsoft YaHei,\\\\5FAE\\8F6F\\96C5\\9ED1,Droid Sans Fallback,Source Sans,Wenquanyi Micro Hei,WenQuanYi Micro Hei Mono,WenQuanYi Zen Hei,Apple LiGothic Medium,SimHei,ST Heiti,WenQuanYi Zen Hei Sharp,sans-serif;"),console.log("")};function S(){w(),g.A.commit(k.yG,j().get(k.yG,i.A.layout)),g.A.commit(k.MV,j().get(k.MV,i.A.fixedHeader)),g.A.commit(k.Fb,j().get(k.Fb,i.A.fixSiderbar)),g.A.commit(k.sl,j().get(k.sl,i.A.contentWidth)),g.A.commit(k.Wb,j().get(k.Wb,i.A.autoHideHeader)),g.A.commit(k.RM,j().get(k.RM,i.A.navTheme)),g.A.commit(k.o6,j().get(k.o6,i.A.colorWeak)),g.A.commit(k.Db,j().get(k.Db,i.A.primaryColor)),g.A.commit(k.jc,j().get(k.jc,i.A.multiTab)),g.A.commit("SET_TOKEN",j().get(k.Xh)),g.A.dispatch("setLang",j().get(k.$C,"zh-CN"))}n(13559);var C=n(56427),x=(n(89999),n(18787)),_=(n(95038),n(31859)),$=(n(31376),n(92422)),T=(n(67598),n(15705)),M=(n(43751),n(34149)),P=(n(89996),n(8442)),z=(n(61443),n(9426)),L=(n(88320),n(60304)),E=(n(78377),n(12393)),U=(n(64291),n(90895)),F=(n(94891),n(50257)),N=(n(98215),n(68263)),O=(n(92283),n(93167)),D=(n(37921),n(27448)),H=(n(5228),n(83766)),R=(n(7225),n(60031)),q=(n(25257),n(97345)),I=(n(24870),n(87298)),V=(n(93316),n(64274)),W=(n(94955),n(90500)),B=(n(78221),n(41446)),Y=(n(17735),n(36457)),G=(n(45870),n(82840)),K=(n(44043),n(39962)),Q=(n(36417),n(65847)),X=(n(53033),n(64719)),Z=(n(85494),n(47132)),J=(n(96205),n(77197)),ee=(n(69941),n(94261)),te=(n(6875),n(98169)),ne=(n(50769),n(40255)),re=(n(47482),n(14248)),se=(n(42290),n(22020)),oe=(n(96305),n(43898)),ie=(n(35184),n(29966)),ae=(n(12854),n(812)),ce=(n(8740),n(71791)),ue=(n(1852),n(73856)),le=(n(40662),n(2546)),de=(n(74721),n(75842)),fe=(n(82187),n(63301)),me=(n(56042),n(91997)),pe=(n(77050),n(49084)),he=(n(70208),n(66066)),ge=(n(92786),n(57155)),be=(n(74332),n(37896)),ye=(n(16878),n(39161)),ve=n(26128),Ae=n(17756),je=n.n(Ae),ke=n(66117),we=n(8815),Se=n(76338),Ce={name:"PageLoading",props:{tip:{type:String,default:"Loading.."},size:{type:String,default:"large"}},render:function(){var e=arguments[0],t={textAlign:"center",background:"rgba(0,0,0,0.6)",position:"fixed",top:0,bottom:0,left:0,right:0,zIndex:1100},n={position:"absolute",left:"50%",top:"40%",transform:"translate(-50%, -50%)"};return e("div",{style:t},[e(G.A,{attrs:{size:this.size,tip:this.tip},style:n})])}},xe="0.0.1",_e={newInstance:function(e,t){var n=document.querySelector("body>div[type=loading]");n||(n=document.createElement("div"),n.setAttribute("type","loading"),n.setAttribute("class","ant-loading-wrapper"),document.body.appendChild(n));var r=Object.assign({visible:!1,size:"large",tip:"Loading..."},t),s=new e({data:function(){return(0,Se.A)({},r)},render:function(){var e=arguments[0],t=this.tip,n={};return this.tip&&(n.tip=t),this.visible?e(Ce,{props:(0,Se.A)({},n)}):null}}).$mount(n);function o(e){var t=(0,Se.A)((0,Se.A)({},r),e),n=t.visible,o=t.size,i=t.tip;s.$set(s,"visible",n),i&&s.$set(s,"tip",i),o&&s.$set(s,"size",o)}return{instance:s,update:o}}},$e={show:function(e){this.instance.update((0,Se.A)((0,Se.A)({},e),{},{visible:!0}))},hide:function(){this.instance.update({visible:!1})}},Te=function(e,t){e.prototype.$loading||($e.instance=_e.newInstance(e,t),e.prototype.$loading=$e)},Me={version:xe,install:Te},Pe=n(15863),ze={add:{key:"add",label:"新增"},delete:{key:"delete",label:"删除"},edit:{key:"edit",label:"修改"},query:{key:"query",label:"查询"},get:{key:"get",label:"详情"},enable:{key:"enable",label:"启用"},disable:{key:"disable",label:"禁用"},import:{key:"import",label:"导入"},export:{key:"export",label:"导出"}};function Le(e){Le.installed||(!e.prototype.$auth&&Object.defineProperties(e.prototype,{$auth:{get:function(){var e=this;return function(t){var n=t.split("."),r=(0,Pe.A)(n,2),s=r[0],o=r[1],i=e.$store.getters.roles.permissions;return i.find((function(e){return e.permissionId===s})).actionList.findIndex((function(e){return e===o}))>-1}}}}),!e.prototype.$enum&&Object.defineProperties(e.prototype,{$enum:{get:function(){return function(e){var t=ze;return e&&e.split(".").forEach((function(e){t=t&&t[e]||null})),t}}}}))}var Ee=Le;r.Ay.directive("action",{inserted:function(e,t,n){t.arg,g.A.getters.roles;var r=n.context.$route.meta.permission;Object.prototype.toString.call(r)}});r.Ay.use(ye.A),r.Ay.use(be.A),r.Ay.use(ge.A),r.Ay.use(he.A),r.Ay.use(pe.A),r.Ay.use(me.A),r.Ay.use(fe.Ay),r.Ay.use(de.A),r.Ay.use(le.Ay),r.Ay.use(ue.A),r.Ay.use(ce.Ay),r.Ay.use(ae.A),r.Ay.use(ie.A),r.Ay.use(oe.A),r.Ay.use(se.A),r.Ay.use(re.Ay),r.Ay.use(ne.A),r.Ay.use(te.A),r.Ay.use(ee.A),r.Ay.use(J.Ay),r.Ay.use(Z.Ay),r.Ay.use(X.A),r.Ay.use(Q.A),r.Ay.use(K.A),r.Ay.use(G.A),r.Ay.use(Y.Ay),r.Ay.use(B.A),r.Ay.use(W.A),r.Ay.use(V.A),r.Ay.use(I.A),r.Ay.use(q.A),r.Ay.use(R.A),r.Ay.use(H.Ay),r.Ay.use(D.Ay),r.Ay.use(O.A),r.Ay.use(N.A),r.Ay.use(F.A),r.Ay.use(U.A),r.Ay.use(E.Ay),r.Ay.use(L.A),r.Ay.use(z.Ay),r.Ay.use(P.Ay),r.Ay.use(M.A),r.Ay.use(T.A),r.Ay.use($.Ay),r.Ay.use(_.A),r.Ay.prototype.$confirm=oe.A.confirm,r.Ay.prototype.$message=x.A,r.Ay.prototype.$notification=C.A,r.Ay.prototype.$info=oe.A.info,r.Ay.prototype.$success=oe.A.success,r.Ay.prototype.$error=oe.A.error,r.Ay.prototype.$warning=oe.A.warning,r.Ay.use(ve.Ay),r.Ay.use(ke.A),r.Ay.use(we.A),r.Ay.use(Me),r.Ay.use(Ee),r.Ay.use(je());var Ue=n(5947),Fe=n.n(Ue);Fe().configure({showSpinner:!1});var Ne=["login","register","registerResult"],Oe="/user/login";h.A.beforeEach((function(e,t,n){Fe().start(),e.meta&&"undefined"!==typeof e.meta.title&&a("".concat((0,u.vb)(e.meta.title)," - ").concat(c));var r=!0;r?e.path===Oe?n():(console.log("store.getters =",g.A.getters),g.A.getters.userInfo&&g.A.getters.userInfo.account?n():g.A.dispatch("GetInfo").then((function(s){console.log("res",s),g.A.dispatch("GenerateRoutes",(0,Se.A)({token:r},s)).then((function(){(0,h.d)(),g.A.getters.addRouters.forEach((function(e){h.A.addRoute(e)}));var r=decodeURIComponent(t.query.redirect||e.path);e.path===r?n((0,Se.A)((0,Se.A)({},e),{},{replace:!0})):n({path:r})}))})).catch((function(t){console.error(t),C.A.error({message:"错误",description:"请求用户信息失败，请重试"}),g.A.dispatch("Logout").then((function(){console.log("logged out"),n({path:Oe,query:{redirect:e.fullPath}})}))}))):Ne.includes(e.name)?n():(n({path:Oe,query:{redirect:e.fullPath}}),Fe().done())})),h.A.afterEach((function(){Fe().done()}));var De=n(95093),He=n.n(De);n(52648);He().locale("zh-cn"),r.Ay.filter("NumberFormat",(function(e){if(!e)return"0";var t=e.toString().replace(/(\d)(?=(?:\d{3})+$)/g,"$1,");return t})),r.Ay.filter("dayjs",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return He()(e).format(t)})),r.Ay.filter("moment",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD HH:mm:ss";return He()(e).format(t)}));var Re=n(29455),qe=n.n(Re);r.Ay.component("vue-ueditor-wrap",qe()),r.Ay.config.productionTip=!1,r.Ay.use(b.He),r.Ay.component("pro-layout",y.Ay),r.Ay.component("page-container",y.sm),window.umi_plugin_ant_themeVar=v.theme,new r.Ay({router:h.A,store:g.A,i18n:u.Ay,created:S,render:function(e){return e(p)}}).$mount("#app")},81625:function(e,t,n){"use strict";n.d(t,{d:function(){return u}});var r=n(85471),s=n(40173),o=n(34962),i=s.Ay.prototype.push;s.Ay.prototype.push=function(e,t,n){return t||n?i.call(this,e,t,n):i.call(this,e).catch((function(e){return e}))},r.Ay.use(s.Ay);var a=function(){return new s.Ay({mode:"history",routes:o.f})},c=a();function u(){var e=a();c.matcher=e.matcher}t.A=c},99547:function(e,t,n){"use strict";n.d(t,{w:function(){return o}});var r=n(76338),s=n(95353),o={computed:(0,r.A)({},(0,s.aH)({isMobile:function(e){return e.app.isMobile}}))}},55499:function(e,t,n){"use strict";n.d(t,{A:function(){return M}});var r,s=n(85471),o=n(95353),i=n(26297),a=(n(26099),n(74053)),c=n.n(a),u=n(75314),l=n(11363),d={state:{sideCollapsed:!1,isMobile:!1,theme:"dark",layout:"topmenu",contentWidth:"",fixedHeader:!1,fixedSidebar:!1,autoHideHeader:!1,color:"",weak:!1,multiTab:!0,lang:"zh-CN",_antLocale:{}},mutations:(r={},(0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)(r,u.cf,(function(e,t){e.sideCollapsed=t,c().set(u.cf,t)})),u.nd,(function(e,t){e.isMobile=t})),u.RM,(function(e,t){e.theme=t,c().set(u.RM,t)})),u.yG,(function(e,t){e.layout=t,c().set(u.yG,t)})),u.MV,(function(e,t){e.fixedHeader=t,c().set(u.MV,t)})),u.Fb,(function(e,t){e.fixedSidebar=t,c().set(u.Fb,t)})),u.sl,(function(e,t){e.contentWidth=t,c().set(u.sl,t)})),u.Wb,(function(e,t){e.autoHideHeader=t,c().set(u.Wb,t)})),u.Db,(function(e,t){e.color=t,c().set(u.Db,t)})),u.o6,(function(e,t){e.weak=t,c().set(u.o6,t)})),(0,i.A)((0,i.A)(r,u.$C,(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e.lang=t,e._antLocale=n,c().set(u.$C,t)})),u.jc,(function(e,t){c().set(u.jc,t),e.multiTab=t}))),actions:{setLang:function(e,t){var n=e.commit;return new Promise((function(e,r){n(u.$C,t),(0,l.J4)(t).then((function(){e()})).catch((function(e){r(e)}))}))}}},f=d,m=n(26398),p=n.n(m),h=n(505),g=n(67569),b=(n(77844),window),y=b.ipcRendererChannel;c().addPlugin(p());var v={state:{token:"",account:"",welcome:"",avatar:"",roles:[],info:{}},mutations:{SET_TOKEN:function(e,t){e.token=t},SET_NAME:function(e,t){var n=t.account,r=t.welcome;e.account=n,e.welcome=r},SET_AVATAR:function(e,t){e.avatar=t},SET_ROLES:function(e,t){e.roles=t},SET_INFO:function(e,t){e.info=t}},actions:{Login:function(e,t){var n=e.commit;return new Promise((function(e,r){(0,h.iD)(t).then((function(t){var s=t.data;if(s.role){var o=s.role;n("SET_ROLES",o),n("SET_INFO",s),n("SET_NAME",{account:s.account,welcome:(0,g.dH)()}),y.PbInfoUpdate.invoke({account:s.pbacct,password:s.pbpwd,url:s.pburl,phone:s.phone}),e(s)}else r(new Error("getInfo: roles must be a non-null array !"))})).catch((function(e){r(e)}))}))},GetInfo:function(e){var t=e.commit;return new Promise((function(e,n){(0,h.Vp)().then((function(r){var s=r.data;s.role?(t("SET_INFO",s),t("SET_NAME",{account:s.account,welcome:(0,g.dH)()}),t("SET_AVATAR",s.avatar),y.PbInfoUpdate.invoke({account:s.pbacct,password:s.pbpwd,url:s.pburl,phone:s.phone}),e(s)):n(new Error("getInfo: roles must be a non-null array !"))})).catch((function(e){n(e)}))}))},Logout:function(e){var t=e.commit,n=e.state;return new Promise((function(e){(0,h.ri)(n.token).then((function(){t("SET_TOKEN",""),t("SET_ROLES",[]),c().remove(u.Xh),y.PbInfoUpdate.invoke({account:"1111111",password:"1111111",url:"https://www.rowilong.com"}),e()})).catch((function(e){console.log("logout fail:",e),y.PbInfoUpdate.invoke({account:"1111111",password:"1111111",url:"https://www.rowilong.com"})})).finally((function(){}))}))}}},A=v,j=(n(28706),n(2008),n(74423),n(21699),n(34962)),k=n(67193),w=n.n(k);function S(e,t){if(t.meta&&t.meta.permission){if(console.log("hasPermission",e),void 0===e)return!1;for(var n=!1,r=0,s=e.length;r<s;r++)if(n=t.meta.permission.includes(e[r]),n)return!0;return!1}return!0}function C(e,t){var n=e.filter((function(e){return console.log("route",e),!!S([t],e)&&(e.children&&e.children.length&&(e.children=C(e.children,t)),!0)}));return n}var x={state:{routers:j.f,addRouters:[]},mutations:{SET_ROUTERS:function(e,t){e.addRouters=t,e.routers=j.f.concat(t)}},actions:{GenerateRoutes:function(e,t){var n=e.commit;return new Promise((function(e){var r=t.role,s=w()(j.y);C(s,r);n("SET_ROUTERS",s),e()}))}}},_=x,$={isMobile:function(e){return e.app.isMobile},lang:function(e){return e.app.lang},theme:function(e){return e.app.theme},color:function(e){return e.app.color},token:function(e){return e.user.token},avatar:function(e){return e.user.avatar},account:function(e){return e.user.account},welcome:function(e){return e.user.welcome},roles:function(e){return e.user.info.role},userInfo:function(e){return e.user.info},userId:function(e){return e.user.info.uuid},addRouters:function(e){return e.permission.addRouters},multiTab:function(e){return e.app.multiTab}},T=$;s.Ay.use(o.Ay);var M=new o.Ay.Store({modules:{app:f,user:A,permission:_},state:{},mutations:{},actions:{},getters:T})},75314:function(e,t,n){"use strict";n.d(t,{$C:function(){return h},Db:function(){return f},Fb:function(){return u},MV:function(){return c},OT:function(){return g},RM:function(){return i},Wb:function(){return d},Xh:function(){return r},cf:function(){return s},jc:function(){return p},nd:function(){return o},o6:function(){return m},sl:function(){return l},yG:function(){return a}});var r="Access-Token",s="sidebar_type",o="is_mobile",i="nav_theme",a="layout",c="fixed_header",u="fixed_sidebar",l="content_width",d="auto_hide_header",f="color",m="weak",p="multi_tab",h="app_language",g={Fluid:"Fluid",Fixed:"Fixed"}},7427:function(e,t,n){n(16280)},75769:function(e,t,n){"use strict";n.d(t,{He:function(){return p},Ay:function(){return h}});var r=n(72505),s=n.n(r),o=n(55499),i=n(74053),a=n.n(i),c=n(81625),u=n(56427),l={vm:{},install:function(e,t){this.installed||(this.installed=!0,t?(e.axios=t,Object.defineProperties(e.prototype,{axios:{get:function(){return t}},$http:{get:function(){return t}}})):console.error("You have to install axios"))}},d=n(75314),f=s().create({baseURL:"/red",timeout:6e4}),m=function(e){if(e.response){var t=e.response.data;a().get(d.Xh);403===e.response.status?u.A.error({message:"Forbidden",description:t.message}):405!==e.response.status&&401!==e.response.status||t.result?u.A.error({message:"error",description:t.error}):(u.A.error({message:"您的登录信息已失效",description:"请重新登陆"}),o.A.dispatch("Logout").then((function(){c.A.push({name:"login"})})))}};f.interceptors.request.use((function(e){var t=a().get(d.Xh);return t&&(e.headers[d.Xh]=t),e}),m),f.interceptors.response.use((function(e){return e.data}),m);var p={vm:{},install:function(e){e.use(l,f)}},h=f},67569:function(e,t,n){"use strict";n.d(t,{Av:function(){return o},Z$:function(){return r},dH:function(){return s}});n(27495);function r(){var e=new Date,t=e.getHours();return t<9?"早上好":t<=11?"上午好":t<=13?"中午好":t<20?"下午好":"晚上好"}function s(){var e=["休息一会儿吧","准备吃什么呢?","要不要打一把 DOTA","我猜你可能累了"],t=Math.floor(Math.random()*e.length);return e[t]}function o(e){var t=0;if(!e)return t;for(var n={},r=0;r<e.length;r++)n[e[r]]=(n[e[r]]||0)+1,t+=5/n[e[r]];var s={digits:/\d/.test(e),lower:/[a-z]/.test(e),upper:/[A-Z]/.test(e),nonWords:/\W/.test(e)},o=0;for(var i in s)o+=!0===s[i]?1:0;return t+=10*(o-1),parseInt(t)}},5839:function(e,t,n){var r={"./en-US":[91758,782],"./en-US.js":[91758,782],"./en-US/account":[21970,980],"./en-US/account.js":[21970,980],"./en-US/account/settings":[36748,98],"./en-US/account/settings.js":[36748,98],"./en-US/dashboard":[12089,345],"./en-US/dashboard.js":[12089,345],"./en-US/dashboard/analysis":[77062,376],"./en-US/dashboard/analysis.js":[77062,376],"./en-US/form":[98031,533],"./en-US/form.js":[98031,533],"./en-US/form/basicForm":[14932,438],"./en-US/form/basicForm.js":[14932,438],"./en-US/global":[12610,418],"./en-US/global.js":[12610,418],"./en-US/menu":[53050,254],"./en-US/menu.js":[53050,254],"./en-US/result":[29780,924],"./en-US/result.js":[29780,924],"./en-US/result/fail":[78915,77],"./en-US/result/fail.js":[78915,77],"./en-US/result/success":[10088,802],"./en-US/result/success.js":[10088,802],"./en-US/setting":[18749,729],"./en-US/setting.js":[18749,729],"./en-US/user":[28666,606],"./en-US/user.js":[28666,606],"./zh-CN":[48188],"./zh-CN.js":[48188],"./zh-CN/account":[77844],"./zh-CN/account.js":[77844],"./zh-CN/account/settings":[54142],"./zh-CN/account/settings.js":[54142],"./zh-CN/dashboard":[91771],"./zh-CN/dashboard.js":[91771],"./zh-CN/dashboard/analysis":[22492],"./zh-CN/dashboard/analysis.js":[22492],"./zh-CN/form":[89065],"./zh-CN/form.js":[89065],"./zh-CN/form/basicForm":[97178],"./zh-CN/form/basicForm.js":[97178],"./zh-CN/global":[39668],"./zh-CN/global.js":[39668],"./zh-CN/menu":[14868],"./zh-CN/menu.js":[14868],"./zh-CN/result":[11250],"./zh-CN/result.js":[11250],"./zh-CN/result/fail":[27669],"./zh-CN/result/fail.js":[27669],"./zh-CN/result/success":[64158],"./zh-CN/result/success.js":[64158],"./zh-CN/setting":[91363],"./zh-CN/setting.js":[91363],"./zh-CN/user":[11016],"./zh-CN/user.js":[11016]};function s(e){if(!n.o(r,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=r[e],s=t[0];return Promise.all(t.slice(1).map(n.e)).then((function(){return n(s)}))}s.keys=function(){return Object.keys(r)},s.id=5839,e.exports=s},33153:function(e,t,n){"use strict";e.exports=n.p+"img/logo.aa78eb32.png"}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var o=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.m=e,function(){var e=[];n.O=function(t,r,s,o){if(!r){var i=1/0;for(l=0;l<e.length;l++){r=e[l][0],s=e[l][1],o=e[l][2];for(var a=!0,c=0;c<r.length;c++)(!1&o||i>=o)&&Object.keys(n.O).every((function(e){return n.O[e](r[c])}))?r.splice(c--,1):(a=!1,o<i&&(i=o));if(a){e.splice(l--,1);var u=s();void 0!==u&&(t=u)}}return t}o=o||0;for(var l=e.length;l>0&&e[l-1][2]>o;l--)e[l]=e[l-1];e[l]=[r,s,o]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,r){return n.f[r](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+({77:"lang-en-US-result-fail",98:"lang-en-US-account-settings",143:"fail",254:"lang-en-US-menu",345:"lang-en-US-dashboard",376:"lang-en-US-dashboard-analysis",418:"lang-en-US-global",438:"lang-en-US-form-basicForm",533:"lang-en-US-form",606:"lang-en-US-user",729:"lang-en-US-setting",782:"lang-en-US",802:"lang-en-US-result-success",806:"user",924:"lang-en-US-result",980:"lang-en-US-account"}[e]||e)+"."+{39:"586d8afb",77:"bcf50c98",96:"5d0c0f6a",98:"674b24e7",143:"1b828c2d",254:"adb693d3",345:"3f9b11a5",376:"3c3cbd50",418:"7b8e9c0c",437:"b14342dd",438:"1f05cf4d",533:"ff561e81",555:"1b19a38c",606:"d00b8773",729:"31d7e673",782:"393cdda6",802:"094e44d5",806:"8098888e",915:"e53cffd2",924:"ed01c776",980:"8ada4800",982:"bcec3fc0"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+(806===e?"user":e)+"."+{39:"8033a4e6",96:"7845c565",437:"6633dcb8",555:"45172cca",806:"65fdd61a",915:"06560b53",982:"ac3e4381"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="vue-antd-pro:";n.l=function(r,s,o,i){if(e[r])e[r].push(s);else{var a,c;if(void 0!==o)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var d=u[l];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){a=d;break}}a||(c=!0,a=document.createElement("script"),a.charset="utf-8",a.timeout=120,n.nc&&a.setAttribute("nonce",n.nc),a.setAttribute("data-webpack",t+o),a.src=r),e[r]=[s];var f=function(t,n){a.onerror=a.onload=null,clearTimeout(m);var s=e[r];if(delete e[r],a.parentNode&&a.parentNode.removeChild(a),s&&s.forEach((function(e){return e(n)})),t)return t(n)},m=setTimeout(f.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=f.bind(null,a.onerror),a.onload=f.bind(null,a.onload),c&&document.head.appendChild(a)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,r,s,o){var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",n.nc&&(i.nonce=n.nc);var a=function(n){if(i.onerror=i.onload=null,"load"===n.type)s();else{var r=n&&n.type,a=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+r+": "+a+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=r,c.request=a,i.parentNode&&i.parentNode.removeChild(i),o(c)}};return i.onerror=i.onload=a,i.href=t,r?r.parentNode.insertBefore(i,r.nextSibling):document.head.appendChild(i),i},t=function(e,t){for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var s=n[r],o=s.getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(o===e||o===t))return s}var i=document.getElementsByTagName("style");for(r=0;r<i.length;r++){s=i[r],o=s.getAttribute("data-href");if(o===e||o===t)return s}},r=function(r){return new Promise((function(s,o){var i=n.miniCssF(r),a=n.p+i;if(t(i,a))return s();e(r,a,null,s,o)}))},s={524:0};n.f.miniCss=function(e,t){var n={39:1,96:1,437:1,555:1,806:1,915:1,982:1};s[e]?t.push(s[e]):0!==s[e]&&n[e]&&t.push(s[e]=r(e).then((function(){s[e]=0}),(function(t){throw delete s[e],t})))}}}(),function(){var e={524:0};n.f.j=function(t,r){var s=n.o(e,t)?e[t]:void 0;if(0!==s)if(s)r.push(s[2]);else{var o=new Promise((function(n,r){s=e[t]=[n,r]}));r.push(s[2]=o);var i=n.p+n.u(t),a=new Error,c=function(r){if(n.o(e,t)&&(s=e[t],0!==s&&(e[t]=void 0),s)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;a.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",a.name="ChunkLoadError",a.type=o,a.request=i,s[1](a)}};n.l(i,c,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,r){var s,o,i=r[0],a=r[1],c=r[2],u=0;if(i.some((function(t){return 0!==e[t]}))){for(s in a)n.o(a,s)&&(n.m[s]=a[s]);if(c)var l=c(n)}for(t&&t(r);u<i.length;u++)o=i[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(l)},r=self["webpackChunkvue_antd_pro"]=self["webpackChunkvue_antd_pro"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var r=n.O(void 0,[504],(function(){return n(45421)}));r=n.O(r)})();