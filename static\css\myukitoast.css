.myk-toast {
	position: relative;
	padding: .75rem 1.25rem;
	margin-left: .5em;
	margin-right: .5em;
	border-radius: .25em;
	border: 1px solid transparent;
	box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
	display: block;
	/* flex: 0 0; */
}

.myk-toast:not(:last-child) {
	margin-bottom: .75rem;
}

.myk-primary {
	color: #004085;
	background-color: #cce5ff;
	border-color: #b8daff;
}

.myk-secondary {
	color: #383d41;
	background-color: #e2e3e5;
	border-color: #d6d8db;
}

.myk-success {
	color: #155724;
	background-color: #d4edda;
	border-color: #c3e6cb;
}

.myk-danger {
	color: #721c24;
	background-color: #f8d7da;
	border-color: #f5c6cb;
}

.myk-warning {
	color: #856404;
	background-color: #fff3cd;
	border-color: #ffeeba;
}

.myk-info {
	color: #0c5460;
	background-color: #d1ecf1;
	border-color: #bee5eb;
}

.myk-light {
	color: #818182;
	background-color: #fefefe;
	border-color: #fdfdfe;
}

.myk-dark {
	color: #1b1e21;
	background-color: #d6d8d9;
	border-color: #c6c8ca;
}

.myk-toastlist {
	position: absolute;
	top: 0;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-direction: column;
	-webkit-flex-direction: column;
	flex-direction: column;
	margin-top: .5em;
}

.myk-toastlist.center {
	left: 50%;
	transform: translateX(-50%);
}

.myk-toastlist.left {
	left: 0;
}

.myk-toastlist.right {
	right: 0;
}

.close {
	display: none;
	position: absolute;
	color: inherit;
	top: 0;
	right: 0;
	cursor: pointer;
	border: 0;
	background-color: transparent;
	font-weight: bold;
	line-height: 1;
	font-size: 1.5rem;
}

.dismissible .close {
	display: block;
}

.close:hover {
	color: #2f3542;
	text-decoration: none;
}

.myk-toast.show {
	display: block;
	opacity: 1;
}

.myk-toast.hide {
	/* position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0; */
	height: 0;
	padding: 0;
	margin: 0;
	border: 0;
}

.nohideanimation {
	display: none;
}

.fade {
	transition: opacity 0.5s linear;
}

.fade:not(.show) {
	opacity: 0;
}

@-webkit-keyframes normal-shake {
	25% {
		-webkit-transform: translateY(-2px);
		transform: translateY(-2px);
	}

	75% {

		-webkit-transform: translateY(2px);
		transform: translateY(2px);
	}
}

/**
 * ----------------------------------------
 * animation normal-shake
 * ----------------------------------------
 */

@keyframes normal-shake {
	25% {
		-webkit-transform: translateY(-2px);
		transform: translateY(-2px);
	}

	75% {

		-webkit-transform: translateY(2px);
		transform: translateY(2px);
	}
}

.normal-shake {
	-webkit-animation: .75s linear infinite normal-shake;
	animation: .75s linear infinite normal-shake;
}

/**
 * Thanks to [Animista - CSS Animations on Demand](https://animista.net/) for providing some cool and elegant animations.
 */

/**
 * ----------------------------------------
 * animation scale-out-center
 * ----------------------------------------
 */
@-webkit-keyframes scale-out-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}

	100% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1;
	}
}

@keyframes scale-out-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}

	100% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1;
	}
}


.scale-out-center {
	-webkit-animation: scale-out-center 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
	animation: scale-out-center 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}

/**
 * ----------------------------------------
 * animation fade-out
 * ----------------------------------------
 */
@-webkit-keyframes fade-out {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

@keyframes fade-out {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}


.fade-out {
	-webkit-animation: fade-out 1s ease-out both;
	animation: fade-out 1s ease-out both;
}


/**
 * ----------------------------------------
 * animation slide-out-top
 * ----------------------------------------
 */
@-webkit-keyframes slide-out-top {
	0% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		opacity: 1;
	}

	100% {
		-webkit-transform: translateY(-1000px);
		transform: translateY(-1000px);
		opacity: 0;
	}
}

@keyframes slide-out-top {
	0% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		opacity: 1;
	}

	100% {
		-webkit-transform: translateY(-1000px);
		transform: translateY(-1000px);
		opacity: 0;
	}
}



.slide-out-top {
	-webkit-animation: slide-out-top 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
	animation: slide-out-top 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}


/**
 * ----------------------------------------
 * animation puff-out-center
 * ----------------------------------------
 */
@-webkit-keyframes puff-out-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-filter: blur(0px);
		filter: blur(0px);
		opacity: 1;
	}

	100% {
		-webkit-transform: scale(2);
		transform: scale(2);
		-webkit-filter: blur(4px);
		filter: blur(4px);
		opacity: 0;
	}
}

@keyframes puff-out-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-filter: blur(0px);
		filter: blur(0px);
		opacity: 1;
	}

	100% {
		-webkit-transform: scale(2);
		transform: scale(2);
		-webkit-filter: blur(4px);
		filter: blur(4px);
		opacity: 0;
	}
}


.puff-out-center {
	-webkit-animation: puff-out-center 1s cubic-bezier(0.165, 0.840, 0.440, 1.000) both;
	animation: puff-out-center 1s cubic-bezier(0.165, 0.840, 0.440, 1.000) both;
}

/**
 * ----------------------------------------
 * animation rotate-out-center
 * ----------------------------------------
 */
@-webkit-keyframes rotate-out-center {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
		opacity: 1;
	}

	100% {
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg);
		opacity: 0;
	}
}

@keyframes rotate-out-center {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
		opacity: 1;
	}

	100% {
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg);
		opacity: 0;
	}
}

.rotate-out-center {
	-webkit-animation: rotate-out-center 0.6s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
	animation: rotate-out-center 0.6s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}

/**
 * ----------------------------------------
 * animation jello-horizontal
 * ----------------------------------------
 */
@-webkit-keyframes jello-horizontal {
	0% {
		-webkit-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1);
	}

	30% {
		-webkit-transform: scale3d(1.25, 0.75, 1);
		transform: scale3d(1.25, 0.75, 1);
	}

	40% {
		-webkit-transform: scale3d(0.75, 1.25, 1);
		transform: scale3d(0.75, 1.25, 1);
	}

	50% {
		-webkit-transform: scale3d(1.15, 0.85, 1);
		transform: scale3d(1.15, 0.85, 1);
	}

	65% {
		-webkit-transform: scale3d(0.95, 1.05, 1);
		transform: scale3d(0.95, 1.05, 1);
	}

	75% {
		-webkit-transform: scale3d(1.05, 0.95, 1);
		transform: scale3d(1.05, 0.95, 1);
	}

	100% {
		-webkit-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1);
	}
}

@keyframes jello-horizontal {
	0% {
		-webkit-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1);
	}

	30% {
		-webkit-transform: scale3d(1.25, 0.75, 1);
		transform: scale3d(1.25, 0.75, 1);
	}

	40% {
		-webkit-transform: scale3d(0.75, 1.25, 1);
		transform: scale3d(0.75, 1.25, 1);
	}

	50% {
		-webkit-transform: scale3d(1.15, 0.85, 1);
		transform: scale3d(1.15, 0.85, 1);
	}

	65% {
		-webkit-transform: scale3d(0.95, 1.05, 1);
		transform: scale3d(0.95, 1.05, 1);
	}

	75% {
		-webkit-transform: scale3d(1.05, 0.95, 1);
		transform: scale3d(1.05, 0.95, 1);
	}

	100% {
		-webkit-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1);
	}
}


.jello-horizontal {
	-webkit-animation: jello-horizontal 0.9s infinite both;
	animation: jello-horizontal 0.9s infinite both;
}

/**
 * ----------------------------------------
 * animation heartbeat
 * ----------------------------------------
 */
@-webkit-keyframes heartbeat {
	from {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-transform-origin: center center;
		transform-origin: center center;
		-webkit-animation-timing-function: ease-out;
		animation-timing-function: ease-out;
	}

	10% {
		-webkit-transform: scale(0.91);
		transform: scale(0.91);
		-webkit-animation-timing-function: ease-in;
		animation-timing-function: ease-in;
	}

	17% {
		-webkit-transform: scale(0.98);
		transform: scale(0.98);
		-webkit-animation-timing-function: ease-out;
		animation-timing-function: ease-out;
	}

	33% {
		-webkit-transform: scale(0.87);
		transform: scale(0.87);
		-webkit-animation-timing-function: ease-in;
		animation-timing-function: ease-in;
	}

	45% {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-animation-timing-function: ease-out;
		animation-timing-function: ease-out;
	}
}

@keyframes heartbeat {
	from {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-transform-origin: center center;
		transform-origin: center center;
		-webkit-animation-timing-function: ease-out;
		animation-timing-function: ease-out;
	}

	10% {
		-webkit-transform: scale(0.91);
		transform: scale(0.91);
		-webkit-animation-timing-function: ease-in;
		animation-timing-function: ease-in;
	}

	17% {
		-webkit-transform: scale(0.98);
		transform: scale(0.98);
		-webkit-animation-timing-function: ease-out;
		animation-timing-function: ease-out;
	}

	33% {
		-webkit-transform: scale(0.87);
		transform: scale(0.87);
		-webkit-animation-timing-function: ease-in;
		animation-timing-function: ease-in;
	}

	45% {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-animation-timing-function: ease-out;
		animation-timing-function: ease-out;
	}
}

.heartbeat {
	-webkit-animation: heartbeat 1.5s ease-in-out infinite both;
	animation: heartbeat 1.5s ease-in-out infinite both;
}

/**
 * ----------------------------------------
 * animation wobble-hor-bottom
 * ----------------------------------------
 */
@-webkit-keyframes wobble-hor-bottom {

	0%,
	100% {
		-webkit-transform: translateX(0%);
		transform: translateX(0%);
		-webkit-transform-origin: 50% 50%;
		transform-origin: 50% 50%;
	}

	15% {
		-webkit-transform: translateX(-30px) rotate(-6deg);
		transform: translateX(-30px) rotate(-6deg);
	}

	30% {
		-webkit-transform: translateX(15px) rotate(6deg);
		transform: translateX(15px) rotate(6deg);
	}

	45% {
		-webkit-transform: translateX(-15px) rotate(-3.6deg);
		transform: translateX(-15px) rotate(-3.6deg);
	}

	60% {
		-webkit-transform: translateX(9px) rotate(2.4deg);
		transform: translateX(9px) rotate(2.4deg);
	}

	75% {
		-webkit-transform: translateX(-6px) rotate(-1.2deg);
		transform: translateX(-6px) rotate(-1.2deg);
	}
}

@keyframes wobble-hor-bottom {

	0%,
	100% {
		-webkit-transform: translateX(0%);
		transform: translateX(0%);
		-webkit-transform-origin: 50% 50%;
		transform-origin: 50% 50%;
	}

	15% {
		-webkit-transform: translateX(-30px) rotate(-6deg);
		transform: translateX(-30px) rotate(-6deg);
	}

	30% {
		-webkit-transform: translateX(15px) rotate(6deg);
		transform: translateX(15px) rotate(6deg);
	}

	45% {
		-webkit-transform: translateX(-15px) rotate(-3.6deg);
		transform: translateX(-15px) rotate(-3.6deg);
	}

	60% {
		-webkit-transform: translateX(9px) rotate(2.4deg);
		transform: translateX(9px) rotate(2.4deg);
	}

	75% {
		-webkit-transform: translateX(-6px) rotate(-1.2deg);
		transform: translateX(-6px) rotate(-1.2deg);
	}
}


.wobble-hor-bottom {
	-webkit-animation: wobble-hor-bottom 0.8s infinite both;
	animation: wobble-hor-bottom 0.8s infinite both;
}


/**
 * ----------------------------------------
 * animation vibrate-1
 * ----------------------------------------
 */
@-webkit-keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
            transform: translate(0);
  }
  20% {
    -webkit-transform: translate(-2px, 2px);
            transform: translate(-2px, 2px);
  }
  40% {
    -webkit-transform: translate(-2px, -2px);
            transform: translate(-2px, -2px);
  }
  60% {
    -webkit-transform: translate(2px, 2px);
            transform: translate(2px, 2px);
  }
  80% {
    -webkit-transform: translate(2px, -2px);
            transform: translate(2px, -2px);
  }
  100% {
    -webkit-transform: translate(0);
            transform: translate(0);
  }
}
@keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
            transform: translate(0);
  }
  20% {
    -webkit-transform: translate(-2px, 2px);
            transform: translate(-2px, 2px);
  }
  40% {
    -webkit-transform: translate(-2px, -2px);
            transform: translate(-2px, -2px);
  }
  60% {
    -webkit-transform: translate(2px, 2px);
            transform: translate(2px, 2px);
  }
  80% {
    -webkit-transform: translate(2px, -2px);
            transform: translate(2px, -2px);
  }
  100% {
    -webkit-transform: translate(0);
            transform: translate(0);
  }
}


.vibrate-1 {
	-webkit-animation: vibrate-1 0.3s linear infinite both;
	        animation: vibrate-1 0.3s linear infinite both;
}

/**
 * Thanks to [Animista - CSS Animations on Demand](https://animista.net/) for providing some cool and elegant animations.
 */